import React, { useState } from 'react';
import { 
  <PERSON><PERSON>lus, 
  <PERSON><PERSON><PERSON>ch, 
  <PERSON><PERSON>ilter, 
  FiUsers, 
  FiAward,
  FiStar,
  FiTrendingUp,
  <PERSON><PERSON><PERSON><PERSON>,
  FiRefreshCw
} from 'react-icons/fi';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import AchievementCard from '../components/achievements/AchievementCard';
import AchievementModal from '../components/achievements/AchievementModal';
import UserProgressCard from '../components/achievements/UserProgressCard';
import { mockDataService } from '../services/mockData';
import achievementService from '../services/achievements';
import { debounce, formatNumber } from '../utils/helpers';
import { ACHIEVEMENT_CATEGORIES, ACHIEVEMENT_RARITIES } from '../utils/constants';

// Use mock service in development mode
const USE_MOCK = import.meta.env.DEV;

const Achievements = () => {
  const [activeTab, setActiveTab] = useState('achievements');
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [rarityFilter, setRarityFilter] = useState('');
  const [selectedAchievement, setSelectedAchievement] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const queryClient = useQueryClient();

  // Fetch achievements
  const { data: achievements = [], isLoading: achievementsLoading, error: achievementsError, refetch: refetchAchievements } = useQuery({
    queryKey: ['achievements', searchTerm, categoryFilter, rarityFilter],
    queryFn: async () => {
      if (USE_MOCK) {
        return await mockDataService.getAchievements({ search: searchTerm, category: categoryFilter, rarity: rarityFilter });
      } else {
        return await achievementService.getAll({ search: searchTerm, category: categoryFilter, rarity: rarityFilter });
      }
    },
    staleTime: 30 * 1000,
  });

  // Fetch user progress
  const { data: userProgress = [], isLoading: progressLoading, error: progressError, refetch: refetchProgress } = useQuery({
    queryKey: ['user-progress'],
    queryFn: async () => {
      if (USE_MOCK) {
        return await mockDataService.getUserProgress();
      } else {
        return await achievementService.getUserProgress();
      }
    },
    staleTime: 60 * 1000,
    enabled: activeTab === 'progress'
  });

  // Fetch achievement stats
  const { data: stats } = useQuery({
    queryKey: ['achievement-stats'],
    queryFn: async () => {
      if (USE_MOCK) {
        return await mockDataService.getAchievementStats();
      } else {
        return await achievementService.getStats();
      }
    },
    staleTime: 5 * 60 * 1000,
  });

  // Create achievement mutation
  const createAchievementMutation = useMutation({
    mutationFn: async (achievementData) => {
      if (USE_MOCK) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        return { id: Date.now(), ...achievementData, createdAt: new Date().toISOString() };
      } else {
        return await achievementService.create(achievementData);
      }
    },
    onSuccess: () => {
      toast.success('Достижение успешно создано');
      queryClient.invalidateQueries(['achievements']);
      queryClient.invalidateQueries(['achievement-stats']);
      setIsModalOpen(false);
      setSelectedAchievement(null);
    },
    onError: (error) => {
      toast.error('Ошибка при создании достижения');
      console.error('Create achievement error:', error);
    }
  });

  // Update achievement mutation
  const updateAchievementMutation = useMutation({
    mutationFn: async ({ id, achievementData }) => {
      if (USE_MOCK) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        return { id, ...achievementData };
      } else {
        return await achievementService.update(id, achievementData);
      }
    },
    onSuccess: () => {
      toast.success('Достижение успешно обновлено');
      queryClient.invalidateQueries(['achievements']);
      setIsModalOpen(false);
      setSelectedAchievement(null);
    },
    onError: (error) => {
      toast.error('Ошибка при обновлении достижения');
      console.error('Update achievement error:', error);
    }
  });

  // Delete achievement mutation
  const deleteAchievementMutation = useMutation({
    mutationFn: async (achievementId) => {
      if (USE_MOCK) {
        await new Promise(resolve => setTimeout(resolve, 500));
        return { success: true };
      } else {
        return await achievementService.delete(achievementId);
      }
    },
    onSuccess: () => {
      toast.success('Достижение успешно удалено');
      queryClient.invalidateQueries(['achievements']);
      queryClient.invalidateQueries(['achievement-stats']);
    },
    onError: (error) => {
      toast.error('Ошибка при удалении достижения');
      console.error('Delete achievement error:', error);
    }
  });

  // Debounced search
  const debouncedSearch = debounce((value) => {
    setSearchTerm(value);
  }, 300);

  const handleSearch = (e) => {
    debouncedSearch(e.target.value);
  };

  const handleCreateAchievement = () => {
    setSelectedAchievement(null);
    setIsModalOpen(true);
  };

  const handleEditAchievement = (achievement) => {
    setSelectedAchievement(achievement);
    setIsModalOpen(true);
  };

  const handleDeleteAchievement = (achievement) => {
    if (window.confirm(`Вы уверены, что хотите удалить достижение "${achievement.name}"?`)) {
      deleteAchievementMutation.mutate(achievement.id);
    }
  };

  const handleSaveAchievement = (achievementData) => {
    if (selectedAchievement) {
      updateAchievementMutation.mutate({ id: selectedAchievement.id, achievementData });
    } else {
      createAchievementMutation.mutate(achievementData);
    }
  };

  const tabs = [
    { id: 'achievements', label: 'Достижения', icon: FiAward },
    { id: 'progress', label: 'Прогресс пользователей', icon: FiUsers },
  ];

  const renderAchievements = () => {
    if (achievementsLoading) {
      return (
        <div className="flex items-center justify-center h-64">
          <FiLoader className="animate-spin h-8 w-8 text-blue-600" />
          <span className="ml-2 text-gray-600">Загрузка достижений...</span>
        </div>
      );
    }

    if (achievementsError) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-red-600 mb-2">Ошибка загрузки достижений</div>
            <button 
              onClick={() => refetchAchievements()} 
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Попробовать снова
            </button>
          </div>
        </div>
      );
    }

    if (achievements.length === 0) {
      return (
        <div className="text-center py-12">
          <FiAward size={48} className="mx-auto text-gray-300 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Достижения не найдены</h3>
          <p className="text-gray-600 mb-4">Создайте первое достижение для мотивации пользователей</p>
          <button
            onClick={handleCreateAchievement}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Создать достижение
          </button>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {achievements.map(achievement => (
          <AchievementCard
            key={achievement.id}
            achievement={achievement}
            onClick={() => handleEditAchievement(achievement)}
            showProgress={false}
          />
        ))}
      </div>
    );
  };

  const renderUserProgress = () => {
    if (progressLoading) {
      return (
        <div className="flex items-center justify-center h-64">
          <FiLoader className="animate-spin h-8 w-8 text-blue-600" />
          <span className="ml-2 text-gray-600">Загрузка прогресса...</span>
        </div>
      );
    }

    if (progressError) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-red-600 mb-2">Ошибка загрузки прогресса</div>
            <button 
              onClick={() => refetchProgress()} 
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Попробовать снова
            </button>
          </div>
        </div>
      );
    }

    if (userProgress.length === 0) {
      return (
        <div className="text-center py-12">
          <FiUsers size={48} className="mx-auto text-gray-300 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Нет данных о прогрессе</h3>
          <p className="text-gray-600">Пользователи еще не начали получать достижения</p>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {userProgress.map(progress => (
          <UserProgressCard
            key={progress.user.id}
            userProgress={progress}
            onClick={() => {
              // Handle user progress details
              console.log('User progress details:', progress);
            }}
          />
        ))}
      </div>
    );
  };

  return (
    <div>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Достижения</h1>
          <p className="text-gray-600 mt-1">
            Управление системой достижений и мотивации пользователей
          </p>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={() => {
              if (activeTab === 'achievements') refetchAchievements();
              if (activeTab === 'progress') refetchProgress();
            }}
            className="flex items-center gap-2 px-3 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            <FiRefreshCw size={16} />
            Обновить
          </button>
          {activeTab === 'achievements' && (
            <button
              onClick={handleCreateAchievement}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <FiPlus size={16} />
              Создать достижение
            </button>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Всего достижений</p>
                <p className="text-2xl font-bold text-gray-900">{formatNumber(stats.totalAchievements)}</p>
              </div>
              <div className="p-3 rounded-full bg-blue-100">
                <FiAward className="text-blue-600" size={24} />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Активных пользователей</p>
                <p className="text-2xl font-bold text-gray-900">{formatNumber(stats.activeUsers)}</p>
              </div>
              <div className="p-3 rounded-full bg-green-100">
                <FiUsers className="text-green-600" size={24} />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Выдано наград</p>
                <p className="text-2xl font-bold text-gray-900">{formatNumber(stats.totalUnlocked)}</p>
              </div>
              <div className="p-3 rounded-full bg-yellow-100">
                <FiStar className="text-yellow-600" size={24} />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Средний прогресс</p>
                <p className="text-2xl font-bold text-gray-900">{stats.averageProgress}%</p>
              </div>
              <div className="p-3 rounded-full bg-purple-100">
                <FiTrendingUp className="text-purple-600" size={24} />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="border-b border-gray-200">
          <div className="flex space-x-8 px-6">
            {tabs.map(tab => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 py-4 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon size={16} />
                  {tab.label}
                </button>
              );
            })}
          </div>
        </div>

        {/* Filters */}
        {activeTab === 'achievements' && (
          <div className="p-6">
            <div className="flex items-center gap-4">
              <div className="flex-1 max-w-md">
                <div className="relative">
                  <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Поиск достижений..."
                    onChange={handleSearch}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
              
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Все категории</option>
                {Object.values(ACHIEVEMENT_CATEGORIES).map(category => (
                  <option key={category} value={category}>
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </option>
                ))}
              </select>
              
              <select
                value={rarityFilter}
                onChange={(e) => setRarityFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Все редкости</option>
                {Object.values(ACHIEVEMENT_RARITIES).map(rarity => (
                  <option key={rarity} value={rarity}>
                    {rarity.charAt(0).toUpperCase() + rarity.slice(1)}
                  </option>
                ))}
              </select>
            </div>
          </div>
        )}
      </div>

      {/* Content */}
      <div>
        {activeTab === 'achievements' ? renderAchievements() : renderUserProgress()}
      </div>

      {/* Achievement Modal */}
      <AchievementModal
        achievement={selectedAchievement}
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setSelectedAchievement(null);
        }}
        onSave={handleSaveAchievement}
        isLoading={createAchievementMutation.isLoading || updateAchievementMutation.isLoading}
      />
    </div>
  );
};

export default Achievements;
