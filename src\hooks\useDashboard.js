import { useQuery } from '@tanstack/react-query';
import { mockDataService } from '../services/mockData';
import systemService from '../services/system';

// Use mock service in development mode
const USE_MOCK = import.meta.env.DEV;

export const useDashboardStats = () => {
  return useQuery({
    queryKey: ['dashboard', 'stats'],
    queryFn: async () => {
      if (USE_MOCK) {
        return await mockDataService.getOverviewStats();
      } else {
        return await systemService.getOverviewStats();
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useRecentUsers = () => {
  return useQuery({
    queryKey: ['dashboard', 'recent-users'],
    queryFn: async () => {
      if (USE_MOCK) {
        const result = await mockDataService.getUsers({ limit: 5 });
        return result.users;
      } else {
        // Real API call would be here
        const result = await mockDataService.getUsers({ limit: 5 });
        return result.users;
      }
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    cacheTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useSystemHealth = () => {
  return useQuery({
    queryKey: ['system', 'health'],
    queryFn: async () => {
      if (USE_MOCK) {
        return await mockDataService.getSystemHealth();
      } else {
        return await systemService.getHealthCheck();
      }
    },
    staleTime: 30 * 1000, // 30 seconds
    cacheTime: 60 * 1000, // 1 minute
    refetchInterval: 60 * 1000, // Refetch every minute
  });
};

export default {
  useDashboardStats,
  useRecentUsers,
  useSystemHealth,
};
