import React, { useState, useEffect } from 'react';
import { FiX, FiInfo } from 'react-icons/fi';
import { useForm, useFieldArray } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import ModuleBuilder from './ModuleBuilder';
import { MODULE_LEVELS } from '../utils/constants';

// Validation schema
const schema = yup.object({
  name: yup
    .string()
    .required('Название модуля обязательно')
    .min(1, 'Минимум 1 символ'),
  level: yup
    .number()
    .required('Уровень модуля обязателен')
    .min(1, 'Минимальный уровень 1')
    .max(5, 'Максимальный уровень 5'),
  pre_requisite_ids: yup
    .array()
    .of(yup.number()),
});

const ModuleModal = ({ 
  module, 
  isOpen, 
  onClose, 
  onSave, 
  isLoading, 
  modules = [],
  theories = [],
  questions = [],
  words = []
}) => {
  const [moduleItems, setModuleItems] = useState([]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    control,
    watch
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      level: 1,
      pre_requisite_ids: []
    }
  });

  const { fields: prerequisiteFields, append: appendPrerequisite, remove: removePrerequisite } = useFieldArray({
    control,
    name: 'pre_requisite_ids'
  });

  const watchedLevel = watch('level');

  useEffect(() => {
    if (isOpen && module) {
      // Edit mode - populate form
      setValue('name', module.name || '');
      setValue('level', module.level || 1);
      setValue('pre_requisite_ids', module.pre_requisite_ids || []);
      
      // Reconstruct module items from IDs
      const items = [];
      
      // Add theories
      if (module.theory_ids) {
        module.theory_ids.forEach((id, index) => {
          const theory = theories.find(t => t.id === id);
          if (theory) {
            items.push({
              id: `theory_${id}_${index}`,
              originalId: id,
              type: 'theory',
              name: theory.name,
              description: theory.content,
              data: theory
            });
          }
        });
      }
      
      // Add questions
      if (module.question_ids) {
        module.question_ids.forEach((id, index) => {
          const question = questions.find(q => q.id === id);
          if (question) {
            items.push({
              id: `question_${id}_${index}`,
              originalId: id,
              type: 'question',
              name: question.question_text,
              description: question.correct_answer,
              data: question
            });
          }
        });
      }
      
      setModuleItems(items);
    } else if (isOpen) {
      // Create mode - reset form
      reset();
      setModuleItems([]);
    }
  }, [isOpen, module, setValue, reset, theories, questions]);

  const handleAddPrerequisite = () => {
    appendPrerequisite(0);
  };

  const getLevelName = (level) => {
    const levels = {
      [MODULE_LEVELS.BEGINNER]: 'Начинающий',
      [MODULE_LEVELS.ELEMENTARY]: 'Элементарный',
      [MODULE_LEVELS.INTERMEDIATE]: 'Средний',
      [MODULE_LEVELS.ADVANCED]: 'Продвинутый',
      [MODULE_LEVELS.EXPERT]: 'Экспертный',
    };
    return levels[level] || `Уровень ${level}`;
  };

  const getAvailablePrerequisites = () => {
    return modules.filter(m => 
      m.id !== module?.id && // Exclude current module
      m.level < watchedLevel // Only lower level modules
    );
  };

  const onSubmit = (data) => {
    // Extract theory and question IDs from module items
    const theoryIds = moduleItems
      .filter(item => item.type === 'theory')
      .map(item => item.originalId);
    
    const questionIds = moduleItems
      .filter(item => item.type === 'question')
      .map(item => item.originalId);

    const moduleData = {
      ...data,
      theory_ids: theoryIds,
      question_ids: questionIds,
      pre_requisite_ids: data.pre_requisite_ids.filter(id => id > 0),
    };

    onSave(moduleData);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {module ? 'Редактировать модуль' : 'Создать модуль'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <FiX size={24} />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="p-6">
          <div className="space-y-6">
            {/* Basic info */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Module name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Название модуля *
                </label>
                <input
                  {...register('name')}
                  type="text"
                  className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.name ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="Введите название модуля"
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                )}
              </div>

              {/* Module level */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Уровень сложности *
                </label>
                <select
                  {...register('level', { valueAsNumber: true })}
                  className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.level ? 'border-red-300' : 'border-gray-300'
                  }`}
                >
                  {Object.values(MODULE_LEVELS).map(level => (
                    <option key={level} value={level}>
                      {getLevelName(level)}
                    </option>
                  ))}
                </select>
                {errors.level && (
                  <p className="mt-1 text-sm text-red-600">{errors.level.message}</p>
                )}
              </div>
            </div>

            {/* Prerequisites */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-gray-700">
                  Предварительные требования
                </label>
                <button
                  type="button"
                  onClick={handleAddPrerequisite}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  Добавить требование
                </button>
              </div>

              {prerequisiteFields.length === 0 && (
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                  <div className="flex items-center gap-2">
                    <FiInfo className="text-blue-600" size={16} />
                    <span className="text-sm text-blue-800">
                      Этот модуль не требует завершения других модулей
                    </span>
                  </div>
                </div>
              )}

              {prerequisiteFields.map((field, index) => (
                <div key={field.id} className="flex items-center gap-2 mb-2">
                  <select
                    {...register(`pre_requisite_ids.${index}`, { valueAsNumber: true })}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value={0}>Выберите модуль</option>
                    {getAvailablePrerequisites().map(prereqModule => (
                      <option key={prereqModule.id} value={prereqModule.id}>
                        {prereqModule.name} ({getLevelName(prereqModule.level)})
                      </option>
                    ))}
                  </select>
                  <button
                    type="button"
                    onClick={() => removePrerequisite(index)}
                    className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded"
                  >
                    <FiX size={16} />
                  </button>
                </div>
              ))}
            </div>

            {/* Module builder */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Содержание модуля
              </label>
              <ModuleBuilder
                moduleItems={moduleItems}
                onItemsChange={setModuleItems}
                theories={theories}
                questions={questions}
                words={words}
              />
              <div className="mt-2 text-sm text-gray-500">
                Всего элементов: {moduleItems.length} 
                (Теории: {moduleItems.filter(i => i.type === 'theory').length}, 
                Вопросы: {moduleItems.filter(i => i.type === 'question').length})
              </div>
            </div>

            {/* Module preview */}
            {moduleItems.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Предварительный просмотр структуры
                </label>
                <div className="bg-gray-50 border border-gray-200 rounded-md p-4 max-h-40 overflow-y-auto">
                  <ol className="space-y-1">
                    {moduleItems.map((item, index) => (
                      <li key={item.id} className="flex items-center gap-2 text-sm">
                        <span className="font-medium text-gray-600">{index + 1}.</span>
                        <span className={`px-2 py-1 rounded text-xs ${
                          item.type === 'theory' ? 'bg-blue-100 text-blue-800' :
                          item.type === 'question' ? 'bg-green-100 text-green-800' :
                          'bg-purple-100 text-purple-800'
                        }`}>
                          {item.type === 'theory' ? 'Теория' : 
                           item.type === 'question' ? 'Вопрос' : 'Слово'}
                        </span>
                        <span className="text-gray-700">{item.name}</span>
                      </li>
                    ))}
                  </ol>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex justify-end gap-3 mt-6 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Отмена
            </button>
            <button
              type="submit"
              disabled={isLoading || moduleItems.length === 0}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Сохранение...' : 'Сохранить'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ModuleModal;
