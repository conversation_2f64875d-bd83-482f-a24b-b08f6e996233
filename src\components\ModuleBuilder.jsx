import React, { useState } from 'react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { FiGripVertical, FiTrash2, FiFileText, FiHelpCircle, FiType } from 'react-icons/fi';

// Sortable item component
const SortableItem = ({ id, item, onRemove, onEdit }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const getItemIcon = (type) => {
    switch (type) {
      case 'theory':
        return <FiFileText className="text-blue-600" size={16} />;
      case 'question':
        return <FiHelpCircle className="text-green-600" size={16} />;
      case 'word':
        return <FiType className="text-purple-600" size={16} />;
      default:
        return <FiFileText className="text-gray-600" size={16} />;
    }
  };

  const getItemTypeName = (type) => {
    switch (type) {
      case 'theory':
        return 'Теория';
      case 'question':
        return 'Вопрос';
      case 'word':
        return 'Слово';
      default:
        return 'Элемент';
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="flex items-center gap-3 p-3 bg-white border border-gray-200 rounded-md shadow-sm hover:shadow-md transition-shadow"
    >
      <button
        className="cursor-grab active:cursor-grabbing text-gray-400 hover:text-gray-600"
        {...attributes}
        {...listeners}
      >
        <FiGripVertical size={16} />
      </button>
      
      <div className="flex items-center gap-2">
        {getItemIcon(item.type)}
        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
          {getItemTypeName(item.type)}
        </span>
      </div>
      
      <div className="flex-1">
        <div className="font-medium text-gray-900">{item.name}</div>
        {item.description && (
          <div className="text-sm text-gray-500">{item.description}</div>
        )}
      </div>
      
      <div className="flex items-center gap-1">
        <button
          onClick={() => onEdit(item)}
          className="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded"
          title="Редактировать"
        >
          <FiFileText size={14} />
        </button>
        <button
          onClick={() => onRemove(id)}
          className="p-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded"
          title="Удалить"
        >
          <FiTrash2 size={14} />
        </button>
      </div>
    </div>
  );
};

// Available items sidebar
const AvailableItems = ({ theories, questions, words, onAddItem }) => {
  const [activeTab, setActiveTab] = useState('theories');

  const tabs = [
    { id: 'theories', label: 'Теории', items: theories, type: 'theory' },
    { id: 'questions', label: 'Вопросы', items: questions, type: 'question' },
    { id: 'words', label: 'Слова', items: words, type: 'word' },
  ];

  const activeTabData = tabs.find(tab => tab.id === activeTab);

  return (
    <div className="w-80 bg-gray-50 border-r border-gray-200 flex flex-col">
      <div className="p-4 border-b border-gray-200">
        <h3 className="font-medium text-gray-900">Доступные элементы</h3>
      </div>
      
      {/* Tabs */}
      <div className="flex border-b border-gray-200">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex-1 px-3 py-2 text-sm font-medium ${
              activeTab === tab.id
                ? 'text-blue-600 border-b-2 border-blue-600 bg-white'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>
      
      {/* Items list */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-2">
          {activeTabData?.items.map(item => (
            <div
              key={item.id}
              className="p-3 bg-white border border-gray-200 rounded-md hover:shadow-sm cursor-pointer"
              onClick={() => onAddItem({ ...item, type: activeTabData.type })}
            >
              <div className="flex items-center gap-2 mb-1">
                {activeTabData.type === 'theory' && <FiFileText className="text-blue-600" size={14} />}
                {activeTabData.type === 'question' && <FiHelpCircle className="text-green-600" size={14} />}
                {activeTabData.type === 'word' && <FiType className="text-purple-600" size={14} />}
                <span className="font-medium text-sm">{item.name || item.kaz_plaintext}</span>
              </div>
              {item.content && (
                <div className="text-xs text-gray-500 line-clamp-2">
                  {item.content.replace(/<[^>]*>/g, '').substring(0, 100)}...
                </div>
              )}
              {item.question_text && (
                <div className="text-xs text-gray-500 line-clamp-2">
                  {item.question_text.substring(0, 100)}...
                </div>
              )}
              {item.rus_plaintext && (
                <div className="text-xs text-gray-500">
                  {item.rus_plaintext}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Main module builder component
const ModuleBuilder = ({ 
  moduleItems = [], 
  onItemsChange, 
  theories = [], 
  questions = [], 
  words = [] 
}) => {
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = moduleItems.findIndex(item => item.id === active.id);
      const newIndex = moduleItems.findIndex(item => item.id === over.id);
      
      const newItems = arrayMove(moduleItems, oldIndex, newIndex);
      onItemsChange(newItems);
    }
  };

  const handleAddItem = (item) => {
    const newItem = {
      id: `${item.type}_${item.id}_${Date.now()}`,
      originalId: item.id,
      type: item.type,
      name: item.name || item.kaz_plaintext || item.question_text,
      description: item.content || item.rus_plaintext || item.correct_answer,
      data: item
    };
    
    onItemsChange([...moduleItems, newItem]);
  };

  const handleRemoveItem = (itemId) => {
    onItemsChange(moduleItems.filter(item => item.id !== itemId));
  };

  const handleEditItem = (item) => {
    // This would open a modal to edit the item
    console.log('Edit item:', item);
  };

  return (
    <div className="flex h-96 border border-gray-200 rounded-lg overflow-hidden">
      {/* Available items sidebar */}
      <AvailableItems
        theories={theories}
        questions={questions}
        words={words}
        onAddItem={handleAddItem}
      />
      
      {/* Module builder area */}
      <div className="flex-1 flex flex-col">
        <div className="p-4 border-b border-gray-200 bg-white">
          <h3 className="font-medium text-gray-900">Структура модуля</h3>
          <p className="text-sm text-gray-500 mt-1">
            Перетащите элементы из левой панели или измените порядок существующих
          </p>
        </div>
        
        <div className="flex-1 p-4 overflow-y-auto bg-gray-50">
          {moduleItems.length === 0 ? (
            <div className="flex items-center justify-center h-full text-gray-500">
              <div className="text-center">
                <FiFileText size={48} className="mx-auto mb-4 text-gray-300" />
                <p>Модуль пуст</p>
                <p className="text-sm">Добавьте элементы из левой панели</p>
              </div>
            </div>
          ) : (
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={moduleItems.map(item => item.id)}
                strategy={verticalListSortingStrategy}
              >
                <div className="space-y-3">
                  {moduleItems.map((item, index) => (
                    <div key={item.id} className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-full text-sm font-medium">
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <SortableItem
                          id={item.id}
                          item={item}
                          onRemove={handleRemoveItem}
                          onEdit={handleEditItem}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </SortableContext>
            </DndContext>
          )}
        </div>
      </div>
    </div>
  );
};

export default ModuleBuilder;
