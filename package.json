{"name": "admin-panel-v2", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.1.1", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.81.5", "@tiptap/extension-bullet-list": "^2.23.1", "@tiptap/extension-image": "^2.23.1", "@tiptap/extension-link": "^2.23.1", "@tiptap/extension-list-item": "^2.23.1", "@tiptap/extension-ordered-list": "^2.23.1", "@tiptap/extension-text-align": "^2.23.1", "@tiptap/react": "^2.23.1", "@tiptap/starter-kit": "^2.23.1", "axios": "^1.10.0", "chart.js": "^4.4.9", "date-fns": "^4.1.0", "lucide-react": "^0.525.0", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-hook-form": "^7.59.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-router-dom": "^7.5.2", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "postcss-import": "^16.1.0", "tailwindcss": "^3.4.1", "vite": "^6.3.1"}}