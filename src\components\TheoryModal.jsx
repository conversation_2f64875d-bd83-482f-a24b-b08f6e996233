import React, { useState, useEffect } from 'react';
import { FiX, FiPlus, FiTrash2, FiTag } from 'react-icons/fi';
import { useForm, useFieldArray } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import RichTextEditor from './RichTextEditor';

// Validation schema
const schema = yup.object({
  name: yup
    .string()
    .required('Название теории обязательно')
    .min(1, 'Минимум 1 символ'),
  content: yup
    .string()
    .required('Содержание теории обязательно')
    .min(10, 'Минимум 10 символов'),
  tags: yup
    .array()
    .of(yup.string().required('Тег не может быть пустым')),
  sentence_ids: yup
    .array()
    .of(yup.number()),
});

const TheoryModal = ({ theory, isOpen, onClose, onSave, isLoading, sentences = [] }) => {
  const [content, setContent] = useState('');

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    control,
    watch
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      tags: [],
      sentence_ids: []
    }
  });

  const { fields: tagFields, append: appendTag, remove: removeTag } = useFieldArray({
    control,
    name: 'tags'
  });

  const { fields: sentenceFields, append: appendSentence, remove: removeSentence } = useFieldArray({
    control,
    name: 'sentence_ids'
  });

  useEffect(() => {
    if (isOpen && theory) {
      // Edit mode - populate form
      setValue('name', theory.name || '');
      setValue('tags', theory.tags || []);
      setValue('sentence_ids', theory.sentence_ids || []);
      setContent(theory.content || '');
    } else if (isOpen) {
      // Create mode - reset form
      reset();
      setContent('');
    }
  }, [isOpen, theory, setValue, reset]);

  const handleAddTag = () => {
    appendTag('');
  };

  const handleAddSentence = () => {
    appendSentence(0);
  };

  const onSubmit = (data) => {
    const theoryData = {
      ...data,
      content,
      tags: data.tags.filter(tag => tag.trim() !== ''),
      sentence_ids: data.sentence_ids.filter(id => id > 0),
    };

    onSave(theoryData);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {theory ? 'Редактировать теорию' : 'Создать теорию'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <FiX size={24} />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="p-6">
          <div className="space-y-6">
            {/* Theory name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Название теории *
              </label>
              <input
                {...register('name')}
                type="text"
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.name ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Введите название теории"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>

            {/* Theory content */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Содержание теории *
              </label>
              <RichTextEditor
                content={content}
                onChange={setContent}
                placeholder="Напишите содержание теории..."
                className="focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              {errors.content && (
                <p className="mt-1 text-sm text-red-600">{errors.content.message}</p>
              )}
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Tags */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Теги
                  </label>
                  <button
                    type="button"
                    onClick={handleAddTag}
                    className="flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800"
                  >
                    <FiPlus size={14} />
                    Добавить тег
                  </button>
                </div>

                {tagFields.length === 0 && (
                  <button
                    type="button"
                    onClick={handleAddTag}
                    className="w-full p-3 border-2 border-dashed border-gray-300 rounded-md text-gray-500 hover:border-gray-400 hover:text-gray-600"
                  >
                    Добавьте теги для категоризации
                  </button>
                )}

                {tagFields.map((field, index) => (
                  <div key={field.id} className="flex items-center gap-2 mb-2">
                    <FiTag className="text-gray-400" size={16} />
                    <input
                      {...register(`tags.${index}`)}
                      type="text"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder={`Тег ${index + 1}`}
                    />
                    <button
                      type="button"
                      onClick={() => removeTag(index)}
                      className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded"
                    >
                      <FiTrash2 size={16} />
                    </button>
                  </div>
                ))}
              </div>

              {/* Related sentences */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Связанные предложения
                  </label>
                  <button
                    type="button"
                    onClick={handleAddSentence}
                    className="flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800"
                  >
                    <FiPlus size={14} />
                    Добавить предложение
                  </button>
                </div>

                {sentenceFields.length === 0 && (
                  <button
                    type="button"
                    onClick={handleAddSentence}
                    className="w-full p-3 border-2 border-dashed border-gray-300 rounded-md text-gray-500 hover:border-gray-400 hover:text-gray-600"
                  >
                    Выберите примеры предложений
                  </button>
                )}

                {sentenceFields.map((field, index) => (
                  <div key={field.id} className="flex items-center gap-2 mb-2">
                    <select
                      {...register(`sentence_ids.${index}`, { valueAsNumber: true })}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value={0}>Выберите предложение</option>
                      {sentences.map(sentence => (
                        <option key={sentence.id} value={sentence.id}>
                          {sentence.kaz_plaintext} - {sentence.rus_plaintext}
                        </option>
                      ))}
                    </select>
                    <button
                      type="button"
                      onClick={() => removeSentence(index)}
                      className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded"
                    >
                      <FiTrash2 size={16} />
                    </button>
                  </div>
                ))}
              </div>
            </div>

            {/* Preview */}
            {content && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Предварительный просмотр
                </label>
                <div className="border border-gray-200 rounded-md p-4 bg-gray-50 max-h-40 overflow-y-auto">
                  <div 
                    className="prose prose-sm max-w-none"
                    dangerouslySetInnerHTML={{ __html: content }}
                  />
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex justify-end gap-3 mt-6 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Отмена
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Сохранение...' : 'Сохранить'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default TheoryModal;
