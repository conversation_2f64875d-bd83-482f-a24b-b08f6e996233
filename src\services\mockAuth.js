// Mock authentication service for testing
export const mockAuthService = {
  // Mock login function
  async login(credentials) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Check test credentials
    if (credentials.email === '<EMAIL>' && credentials.password === 'admin123456') {
      const mockTokens = {
        access_token: 'mock_access_token_' + Date.now(),
        refresh_token: 'mock_refresh_token_' + Date.now()
      };
      
      const mockUser = {
        id: 1,
        name: 'Ад<PERSON><PERSON><PERSON>',
        surname: 'Системы',
        email: '<EMAIL>',
        image_url: '',
        activated: true,
        created_at: new Date().toISOString()
      };
      
      return { tokens: mockTokens, user: mockUser };
    } else {
      throw new Error('Неверные учетные данные');
    }
  },

  // Mock verify token
  async verifyToken() {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const token = localStorage.getItem('accessToken');
    if (token && token.startsWith('mock_access_token_')) {
      return { valid: true };
    } else {
      throw new Error('Invalid token');
    }
  }
};

export default mockAuthService;
