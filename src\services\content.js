import apiClient from './api';

export const contentService = {
  // Words management
  words: {
    async getAll() {
      const response = await apiClient.get('/word');
      return response.data.words;
    },

    async create(wordData) {
      const response = await apiClient.post('/word', wordData);
      return response.data.word;
    },

    async update(wordId, wordData) {
      const response = await apiClient.put(`/word/${wordId}`, wordData);
      return response.data.word;
    },

    async delete(wordId) {
      const response = await apiClient.delete(`/word/${wordId}`);
      return response.data;
    }
  },

  // Sentences management
  sentences: {
    async getAll() {
      const response = await apiClient.get('/sentence/all');
      return response.data.sentences;
    },

    async getById(sentenceId) {
      const response = await apiClient.get(`/sentence?id=${sentenceId}`);
      return response.data.sentence;
    },

    async create(sentenceData) {
      const response = await apiClient.post('/sentence', sentenceData);
      return response.data.sentence;
    },

    async update(sentenceId, sentenceData) {
      const response = await apiClient.put(`/sentence/${sentenceId}`, sentenceData);
      return response.data.sentence;
    },

    async delete(sentenceId) {
      const response = await apiClient.delete(`/sentence/${sentenceId}`);
      return response.data;
    }
  },

  // Questions management
  questions: {
    async getAll() {
      const response = await apiClient.get('/questions/all');
      return response.data.questions;
    },

    async getById(questionId) {
      const response = await apiClient.get(`/questions?id=${questionId}`);
      return response.data.question;
    },

    async create(questionData) {
      const response = await apiClient.post('/questions', questionData);
      return response.data.question;
    },

    async update(questionId, questionData) {
      const response = await apiClient.put(`/questions/${questionId}`, questionData);
      return response.data.question;
    },

    async delete(questionId) {
      const response = await apiClient.delete(`/questions/${questionId}`);
      return response.data;
    }
  },

  // Theories management
  theories: {
    async getAll() {
      const response = await apiClient.get('/theory/all');
      return response.data.theories;
    },

    async getById(theoryId) {
      const response = await apiClient.get(`/theory?id=${theoryId}`);
      return response.data.theory;
    },

    async create(theoryData) {
      const response = await apiClient.post('/theory', theoryData);
      return response.data.theory;
    },

    async update(theoryId, theoryData) {
      const response = await apiClient.put('/theory', {
        id: theoryId,
        ...theoryData
      });
      return response.data.theory;
    },

    async delete(theoryId) {
      const response = await apiClient.delete(`/theory?id=${theoryId}`);
      return response.data;
    }
  },

  // Modules management
  modules: {
    async getAll() {
      const response = await apiClient.get('/module/all');
      return response.data.modules;
    },

    async getById(moduleId) {
      const response = await apiClient.get(`/module?id=${moduleId}`);
      return response.data.module;
    },

    async create(moduleData) {
      const response = await apiClient.post('/module', moduleData);
      return response.data.module;
    },

    async update(moduleId, moduleData) {
      const response = await apiClient.put('/module', {
        id: moduleId,
        ...moduleData
      });
      return response.data.module;
    },

    async delete(moduleId) {
      const response = await apiClient.delete(`/module?id=${moduleId}`);
      return response.data;
    }
  }
};

export default contentService;
