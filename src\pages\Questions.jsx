import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>Edit2, <PERSON><PERSON><PERSON>2, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tag } from 'react-icons/fi';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import QuestionModal from '../components/QuestionModal';
import Table from '../components/Table';
import { mockDataService } from '../services/mockData';
import contentService from '../services/content';
import { debounce, truncateText } from '../utils/helpers';
import { QUESTION_TYPES } from '../utils/constants';

// Use mock service in development mode
const USE_MOCK = import.meta.env.DEV;

// Mock questions data
const mockQuestions = [
  {
    id: 1,
    type: QUESTION_TYPES.TRANSLATION,
    question_text: 'Как сказать "привет" на казахском языке?',
    correct_answer: 'сәлем',
    options: ['сәлем', 'рахмет', 'кешіріңіз', 'сау болыңыз'],
    word_ids: [1],
    audio_url: null,
    image_url: null
  },
  {
    id: 2,
    type: QUESTION_TYPES.AUDIO,
    question_text: 'Что означает это слово?',
    correct_answer: 'спасибо',
    options: ['спасибо', 'привет', 'извините', 'до свидания'],
    word_ids: [2],
    audio_url: 'http://minio:9000/klingo-audio/rahmet.mp3',
    image_url: null
  },
  {
    id: 3,
    type: QUESTION_TYPES.TRANSLATION,
    question_text: 'Переведите "мама" на казахский язык',
    correct_answer: 'ана',
    options: ['ана', 'әке', 'бала', 'дос'],
    word_ids: [4],
    audio_url: null,
    image_url: null
  },
];

const Questions = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [selectedQuestion, setSelectedQuestion] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const queryClient = useQueryClient();

  // Fetch questions
  const { data: questions = [], isLoading, error, refetch } = useQuery({
    queryKey: ['questions', searchTerm, typeFilter],
    queryFn: async () => {
      if (USE_MOCK) {
        let filteredQuestions = [...mockQuestions];
        
        if (searchTerm) {
          filteredQuestions = filteredQuestions.filter(question => 
            question.question_text.toLowerCase().includes(searchTerm.toLowerCase()) ||
            question.correct_answer.toLowerCase().includes(searchTerm.toLowerCase())
          );
        }
        
        if (typeFilter) {
          filteredQuestions = filteredQuestions.filter(question => question.type === typeFilter);
        }
        
        return filteredQuestions;
      } else {
        return await contentService.questions.getAll();
      }
    },
    staleTime: 30 * 1000,
  });

  // Fetch words for modal
  const { data: words = [] } = useQuery({
    queryKey: ['words'],
    queryFn: async () => {
      if (USE_MOCK) {
        return await mockDataService.getWords();
      } else {
        return await contentService.words.getAll();
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Create question mutation
  const createQuestionMutation = useMutation({
    mutationFn: async (questionData) => {
      if (USE_MOCK) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        return { id: Date.now(), ...questionData };
      } else {
        return await contentService.questions.create(questionData);
      }
    },
    onSuccess: () => {
      toast.success('Вопрос успешно создан');
      queryClient.invalidateQueries(['questions']);
      setIsModalOpen(false);
      setSelectedQuestion(null);
    },
    onError: (error) => {
      toast.error('Ошибка при создании вопроса');
      console.error('Create question error:', error);
    }
  });

  // Update question mutation
  const updateQuestionMutation = useMutation({
    mutationFn: async ({ id, questionData }) => {
      if (USE_MOCK) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        return { id, ...questionData };
      } else {
        return await contentService.questions.update(id, questionData);
      }
    },
    onSuccess: () => {
      toast.success('Вопрос успешно обновлен');
      queryClient.invalidateQueries(['questions']);
      setIsModalOpen(false);
      setSelectedQuestion(null);
    },
    onError: (error) => {
      toast.error('Ошибка при обновлении вопроса');
      console.error('Update question error:', error);
    }
  });

  // Delete question mutation
  const deleteQuestionMutation = useMutation({
    mutationFn: async (questionId) => {
      if (USE_MOCK) {
        await new Promise(resolve => setTimeout(resolve, 500));
        return { success: true };
      } else {
        return await contentService.questions.delete(questionId);
      }
    },
    onSuccess: () => {
      toast.success('Вопрос успешно удален');
      queryClient.invalidateQueries(['questions']);
    },
    onError: (error) => {
      toast.error('Ошибка при удалении вопроса');
      console.error('Delete question error:', error);
    }
  });

  // Debounced search
  const debouncedSearch = debounce((value) => {
    setSearchTerm(value);
  }, 300);

  const handleSearch = (e) => {
    debouncedSearch(e.target.value);
  };

  const handleCreateQuestion = () => {
    setSelectedQuestion(null);
    setIsModalOpen(true);
  };

  const handleEditQuestion = (question) => {
    setSelectedQuestion(question);
    setIsModalOpen(true);
  };

  const handleDeleteQuestion = (question) => {
    if (window.confirm(`Вы уверены, что хотите удалить вопрос "${truncateText(question.question_text, 50)}"?`)) {
      deleteQuestionMutation.mutate(question.id);
    }
  };

  const handleSaveQuestion = (questionData) => {
    if (selectedQuestion) {
      updateQuestionMutation.mutate({ id: selectedQuestion.id, questionData });
    } else {
      createQuestionMutation.mutate(questionData);
    }
  };

  const getQuestionTypeLabel = (type) => {
    const labels = {
      [QUESTION_TYPES.TRANSLATION]: 'Перевод',
      [QUESTION_TYPES.AUDIO]: 'Аудио',
      [QUESTION_TYPES.IMAGE]: 'Изображение',
      [QUESTION_TYPES.SENTENCE_CONSTRUCTION]: 'Конструирование',
    };
    return labels[type] || type;
  };

  const getQuestionTypeColor = (type) => {
    const colors = {
      [QUESTION_TYPES.TRANSLATION]: 'bg-blue-100 text-blue-800',
      [QUESTION_TYPES.AUDIO]: 'bg-green-100 text-green-800',
      [QUESTION_TYPES.IMAGE]: 'bg-purple-100 text-purple-800',
      [QUESTION_TYPES.SENTENCE_CONSTRUCTION]: 'bg-orange-100 text-orange-800',
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  const getRelatedWords = (wordIds) => {
    if (!wordIds || wordIds.length === 0) return [];
    return wordIds
      .map(id => words.find(word => word.id === id))
      .filter(Boolean);
  };

  // Table columns
  const columns = [
    {
      header: 'Тип',
      accessor: 'type',
      render: (row) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getQuestionTypeColor(row.type)}`}>
          {getQuestionTypeLabel(row.type)}
        </span>
      )
    },
    {
      header: 'Вопрос',
      accessor: 'question_text',
      render: (row) => (
        <div className="max-w-xs">
          <div className="font-medium text-gray-900">
            {truncateText(row.question_text, 80)}
          </div>
        </div>
      )
    },
    {
      header: 'Правильный ответ',
      accessor: 'correct_answer',
      render: (row) => (
        <div className="font-medium text-green-600">
          {truncateText(row.correct_answer, 30)}
        </div>
      )
    },
    {
      header: 'Связанные слова',
      accessor: 'word_ids',
      render: (row) => {
        const relatedWords = getRelatedWords(row.word_ids);
        return (
          <div className="flex items-center gap-1">
            <FiTag size={14} className="text-gray-400" />
            <span className="text-sm text-gray-600">
              {relatedWords.length > 0 
                ? relatedWords.map(word => word.kaz_plaintext).join(', ')
                : 'Нет слов'
              }
            </span>
          </div>
        );
      }
    },
    {
      header: 'Варианты',
      accessor: 'options',
      render: (row) => (
        <div className="text-sm text-gray-600">
          {row.options?.length || 0} вариантов
        </div>
      )
    },
    {
      header: 'Действия',
      accessor: 'actions',
      render: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleEditQuestion(row)}
            className="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded transition-colors"
            title="Редактировать"
          >
            <FiEdit2 size={16} />
          </button>
          <button
            onClick={() => handleDeleteQuestion(row)}
            className="p-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded transition-colors"
            title="Удалить"
          >
            <FiTrash2 size={16} />
          </button>
        </div>
      )
    }
  ];

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-red-600 mb-2">Ошибка загрузки вопросов</div>
          <button 
            onClick={() => refetch()} 
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Попробовать снова
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Вопросы</h1>
          <p className="text-gray-600 mt-1">
            Всего вопросов: {questions.length}
          </p>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={handleCreateQuestion}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <FiPlus size={16} />
            Создать вопрос
          </button>
        </div>
      </div>

      {/* Search and filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="flex items-center gap-4">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Поиск по тексту вопроса или ответу..."
                onChange={handleSearch}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
          <div className="flex items-center gap-2">
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Все типы</option>
              <option value={QUESTION_TYPES.TRANSLATION}>Перевод</option>
              <option value={QUESTION_TYPES.AUDIO}>Аудио</option>
              <option value={QUESTION_TYPES.IMAGE}>Изображение</option>
              <option value={QUESTION_TYPES.SENTENCE_CONSTRUCTION}>Конструирование</option>
            </select>
          </div>
        </div>
      </div>

      {/* Questions Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <FiLoader className="animate-spin h-8 w-8 text-blue-600" />
            <span className="ml-2 text-gray-600">Загрузка вопросов...</span>
          </div>
        ) : (
          <Table 
            columns={columns} 
            data={questions}
          />
        )}
      </div>

      {/* Question Modal */}
      <QuestionModal
        question={selectedQuestion}
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setSelectedQuestion(null);
        }}
        onSave={handleSaveQuestion}
        isLoading={createQuestionMutation.isLoading || updateQuestionMutation.isLoading}
        words={words}
      />
    </div>
  );
};

export default Questions;
