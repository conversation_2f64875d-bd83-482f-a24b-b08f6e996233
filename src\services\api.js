import axios from 'axios';
import toast from 'react-hot-toast';

// API Configuration
const API_BASE_URL = 'http://localhost:8080/v1';

// Create axios instance
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('accessToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response) {
      const { status, data } = error.response;

      switch (status) {
        case 401:
          // Unauthorized - redirect to login
          localStorage.removeItem('accessToken');
          localStorage.removeItem('refreshToken');
          window.location.href = '/login';
          toast.error('Сессия истекла. Пожалуйста, войдите снова.');
          break;
        case 403:
          toast.error('Недостаточно прав доступа');
          break;
        case 404:
          toast.error('Ресурс не найден');
          break;
        case 422:
          toast.error('Ошибка валидации данных');
          break;
        case 429:
          toast.error('Превышен лимит запросов');
          break;
        case 500:
          toast.error('Внутренняя ошибка сервера');
          break;
        default:
          toast.error(data?.error || 'Произошла ошибка');
      }
    } else if (error.request) {
      toast.error('Ошибка сети. Проверьте подключение к интернету.');
    } else {
      toast.error('Произошла неизвестная ошибка');
    }

    return Promise.reject(error);
  }
);

export default apiClient;
