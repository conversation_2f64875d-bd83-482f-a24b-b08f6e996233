// API Configuration
export const API_CONFIG = {
  BASE_URL: 'http://localhost:8080/v1',
  TIMEOUT: 10000,
};

// User Status
export const USER_STATUS = {
  ACTIVE: 'active',
  BLOCKED: 'blocked',
};

// Question Types
export const QUESTION_TYPES = {
  TRANSLATION: 'translation',
  AUDIO: 'audio',
  IMAGE: 'image',
  SENTENCE_CONSTRUCTION: 'sentence_construction',
};




// Achievement Types
export const ACHIEVEMENT_TYPES = {
  MODULE_COMPLETION: 'module_completion',
  STREAK: 'streak',
  TOTAL_MODULES: 'total_modules',
  PERFECT_SCORE: 'perfect_score',
};

// File Types
export const FILE_TYPES = {
  AUDIO: 'audio',
  IMAGE: 'image',
};

// Supported file formats
export const SUPPORTED_FORMATS = {
  IMAGES: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'],
  AUDIO: ['mp3', 'wav', 'ogg', 'm4a', 'aac'],
  DOCUMENTS: ['pdf', 'doc', 'docx', 'txt']
};

// File size limits (in MB)
export const FILE_SIZE_LIMITS = {
  SINGLE: 10, // MB
  MULTIPLE: 50, // MB total
  IMAGE: 5, // MB
  AUDIO: 20, // MB
  DOCUMENT: 10 // MB
};


// MinIO buckets
export const MINIO_BUCKETS = {
  AUDIO: 'klingo-audio',
  IMAGES: 'klingo-images',
};

// Colors for UI
export const COLORS = {
  PRIMARY: '#1976d2',
  SECONDARY: '#dc004e',
  SUCCESS: '#388e3c',
  WARNING: '#f57c00',
  ERROR: '#d32f2f',
  BACKGROUND: '#f5f5f5',
};

// Pagination defaults
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 20,
  LIMIT_OPTIONS: [20, 50, 100],
};

// Module levels
export const MODULE_LEVELS = {
  BEGINNER: 1,
  ELEMENTARY: 2,
  INTERMEDIATE: 3,
  ADVANCED: 4,
  EXPERT: 5,
};

// Sort orders
export const SORT_ORDERS = {
  ASC: 'asc',
  DESC: 'desc',
};

// Date formats
export const DATE_FORMATS = {
  DISPLAY: 'DD.MM.YYYY',
  DISPLAY_WITH_TIME: 'DD.MM.YYYY HH:mm',
  API: 'YYYY-MM-DD',
  ISO: 'YYYY-MM-DDTHH:mm:ss.SSSZ',
};

// Navigation menu items
export const MENU_ITEMS = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    path: '/dashboard',
    icon: 'BarChart3',
  },
  {
    id: 'users',
    label: 'Пользователи',
    path: '/users',
    icon: 'Users',
  },
  {
    id: 'content',
    label: 'Контент',
    icon: 'BookOpen',
    children: [
      {
        id: 'words',
        label: 'Словарь',
        path: '/content/words',
        icon: 'Type',
      },
      {
        id: 'sentences',
        label: 'Предложения',
        path: '/content/sentences',
        icon: 'MessageSquare',
      },
      {
        id: 'questions',
        label: 'Вопросы',
        path: '/content/questions',
        icon: 'HelpCircle',
      },
      {
        id: 'theories',
        label: 'Теории',
        path: '/content/theories',
        icon: 'FileText',
      },
      {
        id: 'modules',
        label: 'Модули',
        path: '/content/modules',
        icon: 'Package',
      },
    ],
  },
  {
    id: 'files',
    label: 'Файлы',
    path: '/files',
    icon: 'Folder',
  },
  {
    id: 'analytics',
    label: 'Аналитика',
    path: '/analytics',
    icon: 'TrendingUp',
  },
  {
    id: 'achievements',
    label: 'Достижения',
    path: '/achievements',
    icon: 'Award',
  },
  {
    id: 'settings',
    label: 'Настройки',
    path: '/settings',
    icon: 'Settings',
  },
];

export default {
  API_CONFIG,
  USER_STATUS,
  QUESTION_TYPES,
  ACHIEVEMENT_TYPES,
  FILE_TYPES,
  SUPPORTED_FORMATS,
  FILE_SIZE_LIMITS,
  MINIO_BUCKETS,
  COLORS,
  PAGINATION,
  MODULE_LEVELS,
  SORT_ORDERS,
  DATE_FORMATS,
  MENU_ITEMS,
};
