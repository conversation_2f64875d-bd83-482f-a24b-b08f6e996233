import apiClient from './api';

const achievementService = {
  // Get all achievements with optional filters
  getAll: async (params = {}) => {
    const response = await apiClient.get('/achievements', { params });
    return response.data;
  },

  // Get achievement by ID
  getById: async (id) => {
    const response = await apiClient.get(`/achievements/${id}`);
    return response.data;
  },

  // Create new achievement
  create: async (achievementData) => {
    const response = await apiClient.post('/achievements', achievementData);
    return response.data;
  },

  // Update achievement
  update: async (id, achievementData) => {
    const response = await apiClient.put(`/achievements/${id}`, achievementData);
    return response.data;
  },

  // Delete achievement
  delete: async (id) => {
    const response = await apiClient.delete(`/achievements/${id}`);
    return response.data;
  },

  // Get achievement statistics
  getStats: async () => {
    const response = await apiClient.get('/achievements/stats');
    return response.data;
  },

  // Get user progress for all users
  getUserProgress: async (params = {}) => {
    const response = await apiClient.get('/achievements/user-progress', { params });
    return response.data;
  },

  // Get specific user's achievement progress
  getUserAchievements: async (userId) => {
    const response = await apiClient.get(`/achievements/users/${userId}`);
    return response.data;
  },

  // Award achievement to user
  awardToUser: async (achievementId, userId, progress = null) => {
    const response = await apiClient.post(`/achievements/${achievementId}/award`, {
      userId,
      progress
    });
    return response.data;
  },

  // Update user's achievement progress
  updateUserProgress: async (achievementId, userId, progress) => {
    const response = await apiClient.patch(`/achievements/${achievementId}/users/${userId}`, {
      progress
    });
    return response.data;
  },

  // Get achievement leaderboard
  getLeaderboard: async (params = {}) => {
    const response = await apiClient.get('/achievements/leaderboard', { params });
    return response.data;
  },

  // Get achievement categories
  getCategories: async () => {
    const response = await apiClient.get('/achievements/categories');
    return response.data;
  },

  // Bulk operations
  bulkAward: async (achievementId, userIds) => {
    const response = await apiClient.post(`/achievements/${achievementId}/bulk-award`, {
      userIds
    });
    return response.data;
  },

  bulkDelete: async (achievementIds) => {
    const response = await apiClient.delete('/achievements/bulk', {
      data: { ids: achievementIds }
    });
    return response.data;
  },

  // Achievement validation
  validateRequirements: async (achievementId, userId) => {
    const response = await apiClient.post(`/achievements/${achievementId}/validate`, {
      userId
    });
    return response.data;
  },

  // Get achievement analytics
  getAnalytics: async (achievementId, dateRange = '30d') => {
    const response = await apiClient.get(`/achievements/${achievementId}/analytics`, {
      params: { dateRange }
    });
    return response.data;
  },

  // Export achievements data
  exportData: async (format = 'json') => {
    const response = await apiClient.get('/achievements/export', {
      params: { format },
      responseType: 'blob'
    });
    
    const blob = new Blob([response.data], { 
      type: format === 'csv' ? 'text/csv' : 'application/json' 
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `achievements.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    
    return response.data;
  },

  // Import achievements data
  importData: async (file) => {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await apiClient.post('/achievements/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    return response.data;
  }
};

export default achievementService;
