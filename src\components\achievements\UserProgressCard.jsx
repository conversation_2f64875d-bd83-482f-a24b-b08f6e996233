import React from 'react';
import { 
  <PERSON><PERSON>ser, 
  FiAward, 
  FiStar, 
  FiTrendingUp, 
  FiTarget,
  FiCalendar,
  FiActivity
} from 'react-icons/fi';
import { formatNumber, formatDate } from '../../utils/helpers';

const UserProgressCard = ({ userProgress, onClick }) => {
  const {
    user,
    totalPoints = 0,
    totalAchievements = 0,
    unlockedAchievements = 0,
    level = 1,
    experiencePoints = 0,
    nextLevelPoints = 1000,
    streak = 0,
    lastActivity,
    recentAchievements = [],
    categoryProgress = {}
  } = userProgress;

  const progressPercentage = Math.min((experiencePoints / nextLevelPoints) * 100, 100);
  const completionRate = totalAchievements > 0 ? (unlockedAchievements / totalAchievements) * 100 : 0;

  const getLevelColor = (level) => {
    if (level >= 50) return 'from-purple-500 to-pink-500';
    if (level >= 30) return 'from-blue-500 to-purple-500';
    if (level >= 20) return 'from-green-500 to-blue-500';
    if (level >= 10) return 'from-yellow-500 to-green-500';
    return 'from-gray-400 to-gray-500';
  };

  const getStreakColor = (streak) => {
    if (streak >= 30) return 'text-purple-600 bg-purple-100';
    if (streak >= 14) return 'text-blue-600 bg-blue-100';
    if (streak >= 7) return 'text-green-600 bg-green-100';
    if (streak >= 3) return 'text-yellow-600 bg-yellow-100';
    return 'text-gray-600 bg-gray-100';
  };

  return (
    <div 
      className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
      onClick={onClick}
    >
      {/* User Header */}
      <div className="flex items-center gap-4 mb-6">
        <div className="relative">
          {user?.avatar ? (
            <img 
              src={user.avatar} 
              alt={user.name}
              className="w-16 h-16 rounded-full object-cover"
            />
          ) : (
            <div className="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center">
              <FiUser size={24} className="text-gray-500" />
            </div>
          )}
          
          {/* Level badge */}
          <div className={`absolute -bottom-1 -right-1 w-8 h-8 rounded-full bg-gradient-to-r ${getLevelColor(level)} flex items-center justify-center text-white text-xs font-bold shadow-lg`}>
            {level}
          </div>
        </div>
        
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900">{user?.name || 'Пользователь'}</h3>
          <p className="text-sm text-gray-600">{user?.email}</p>
          
          {/* Streak */}
          <div className="flex items-center gap-2 mt-1">
            <div className={`px-2 py-1 rounded-full text-xs font-medium ${getStreakColor(streak)}`}>
              🔥 {streak} дней подряд
            </div>
            {lastActivity && (
              <span className="text-xs text-gray-500">
                Последняя активность: {formatDate(lastActivity)}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="text-center">
          <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-full mx-auto mb-2">
            <FiStar className="text-blue-600" size={20} />
          </div>
          <div className="text-lg font-bold text-gray-900">{formatNumber(totalPoints)}</div>
          <div className="text-xs text-gray-600">Очков</div>
        </div>
        
        <div className="text-center">
          <div className="flex items-center justify-center w-10 h-10 bg-green-100 rounded-full mx-auto mb-2">
            <FiAward className="text-green-600" size={20} />
          </div>
          <div className="text-lg font-bold text-gray-900">{unlockedAchievements}</div>
          <div className="text-xs text-gray-600">Достижений</div>
        </div>
        
        <div className="text-center">
          <div className="flex items-center justify-center w-10 h-10 bg-purple-100 rounded-full mx-auto mb-2">
            <FiTrendingUp className="text-purple-600" size={20} />
          </div>
          <div className="text-lg font-bold text-gray-900">{level}</div>
          <div className="text-xs text-gray-600">Уровень</div>
        </div>
        
        <div className="text-center">
          <div className="flex items-center justify-center w-10 h-10 bg-orange-100 rounded-full mx-auto mb-2">
            <FiTarget className="text-orange-600" size={20} />
          </div>
          <div className="text-lg font-bold text-gray-900">{completionRate.toFixed(0)}%</div>
          <div className="text-xs text-gray-600">Завершено</div>
        </div>
      </div>

      {/* Level Progress */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-gray-700">
            Прогресс до уровня {level + 1}
          </span>
          <span className="text-sm text-gray-600">
            {formatNumber(experiencePoints)} / {formatNumber(nextLevelPoints)} XP
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div 
            className={`h-3 rounded-full bg-gradient-to-r ${getLevelColor(level)} transition-all duration-500`}
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
        <div className="text-xs text-gray-500 mt-1">
          {progressPercentage.toFixed(1)}% до следующего уровня
        </div>
      </div>

      {/* Category Progress */}
      {Object.keys(categoryProgress).length > 0 && (
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Прогресс по категориям</h4>
          <div className="space-y-2">
            {Object.entries(categoryProgress).map(([category, progress]) => (
              <div key={category} className="flex items-center justify-between">
                <span className="text-sm text-gray-600 capitalize">{category}</span>
                <div className="flex items-center gap-2">
                  <div className="w-20 bg-gray-200 rounded-full h-2">
                    <div 
                      className="h-2 bg-blue-500 rounded-full transition-all duration-300"
                      style={{ width: `${Math.min(progress.percentage || 0, 100)}%` }}
                    />
                  </div>
                  <span className="text-xs text-gray-500 w-12 text-right">
                    {progress.unlocked || 0}/{progress.total || 0}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Recent Achievements */}
      {recentAchievements.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-3">Последние достижения</h4>
          <div className="space-y-2">
            {recentAchievements.slice(0, 3).map((achievement, index) => (
              <div key={index} className="flex items-center gap-3 p-2 bg-gray-50 rounded-md">
                <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                  <FiAward className="text-yellow-600" size={14} />
                </div>
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-900">{achievement.name}</div>
                  <div className="text-xs text-gray-500">
                    {formatDate(achievement.unlockedAt)} • +{achievement.points} очков
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Activity Indicator */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center gap-1">
            <FiActivity size={12} />
            <span>Активность</span>
          </div>
          <div className="flex items-center gap-1">
            <FiCalendar size={12} />
            <span>Присоединился {formatDate(user?.createdAt)}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProgressCard;
