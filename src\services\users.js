import apiClient from './api';

export const usersService = {
  // Get all users with filters
  async getUsers(filters = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value) params.append(key, value.toString());
    });

    const response = await apiClient.get(`/admin/users?${params}`);
    return response.data;
  },

  // Get user details
  async getUserDetails(userId) {
    const response = await apiClient.get(`/admin/users/${userId}`);
    return response.data;
  },

  // Update user status
  async updateUserStatus(userId, status, reason) {
    const response = await apiClient.put(`/admin/users/${userId}/status`, {
      status,
      reason
    });
    return response.data;
  },

  // Get user progress
  async getUserProgress(userId) {
    const response = await apiClient.get(`/module-user-progress/${userId}`);
    return response.data;
  },

  // Get user streak
  async getUserStreak(userId) {
    const response = await apiClient.get(`/progress/streak/${userId}`);
    return response.data;
  },

  // Get user achievements
  async getUserAchievements(userId) {
    const response = await apiClient.get(`/achievements/${userId}`);
    return response.data;
  }
};

export default usersService;
