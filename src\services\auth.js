import apiClient from './api';

// Authentication service
export const authService = {
  // Login admin
  async login(credentials) {
    try {
      const response = await apiClient.post('/auth/login', credentials);
      const { tokens, user } = response.data;
      
      // Save tokens to localStorage
      localStorage.setItem('accessToken', tokens.access_token);
      localStorage.setItem('refreshToken', tokens.refresh_token);
      localStorage.setItem('user', JSON.stringify(user));
      
      return { tokens, user };
    } catch (error) {
      throw error;
    }
  },

  // Logout
  logout() {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');
    window.location.href = '/login';
  },

  // Get current user from localStorage
  getCurrentUser() {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  },

  // Check if user is authenticated
  isAuthenticated() {
    const token = localStorage.getItem('accessToken');
    return !!token;
  },

  // Verify token (placeholder for future implementation)
  async verifyToken() {
    try {
      // This endpoint might need to be implemented on the backend
      const response = await apiClient.get('/auth/verify');
      return response.data;
    } catch (error) {
      // If verification fails, logout
      this.logout();
      throw error;
    }
  }
};

export default authService;
