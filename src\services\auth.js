import apiClient from './api';
import mockAuthService from './mockAuth';

// Use mock service in development mode
const USE_MOCK = import.meta.env.DEV;

// Authentication service
export const authService = {
  // Login admin
  async login(credentials) {
    try {
      let response;

      if (USE_MOCK) {
        // Use mock service in development
        response = await mockAuthService.login(credentials);
      } else {
        // Use real API in production
        const apiResponse = await apiClient.post('/auth/login', credentials);
        response = apiResponse.data;
      }

      const { tokens, user } = response;

      // Save tokens to localStorage
      localStorage.setItem('accessToken', tokens.access_token);
      localStorage.setItem('refreshToken', tokens.refresh_token);
      localStorage.setItem('user', JSON.stringify(user));

      return { tokens, user };
    } catch (error) {
      throw error;
    }
  },

  // Logout
  logout() {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');
    window.location.href = '/login';
  },

  // Get current user from localStorage
  getCurrentUser() {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  },

  // Check if user is authenticated
  isAuthenticated() {
    const token = localStorage.getItem('accessToken');
    return !!token;
  },

  // Verify token
  async verifyToken() {
    try {
      if (USE_MOCK) {
        // Use mock service in development
        return await mockAuthService.verifyToken();
      } else {
        // Use real API in production
        const response = await apiClient.get('/auth/verify');
        return response.data;
      }
    } catch (error) {
      // If verification fails, logout
      this.logout();
      throw error;
    }
  }
};

export default authService;
