// Mock data for development and testing

export const mockStats = {
  overview: {
    total_users: 1247,
    active_users_today: 89,
    active_users_week: 456,
    total_modules: 24,
    total_questions: 450,
    total_words: 3456,
    avg_completion_rate: 67.8,
    popular_modules: [
      { id: 1, name: "Базовые приветствия", users_count: 234 },
      { id: 2, name: "Семья и родственники", users_count: 189 },
      { id: 3, name: "Еда и напитки", users_count: 156 },
      { id: 4, name: "Время и даты", users_count: 134 },
      { id: 5, name: "Цвета и формы", users_count: 112 }
    ]
  },
  
  users: [
    {
      id: 1,
      name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      surname: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", 
      email: "<EMAIL>",
      created_at: "2024-01-24T10:30:00Z",
      last_activity: "2024-01-25T15:45:00Z",
      status: "active",
      progress: 85
    },
    {
      id: 2,
      name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      surname: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
      email: "<EMAIL>", 
      created_at: "2024-01-23T14:20:00Z",
      last_activity: "2024-01-25T12:30:00Z",
      status: "active",
      progress: 67
    },
    {
      id: 3,
      name: "Данияр",
      surname: "Касымов",
      email: "<EMAIL>",
      created_at: "2024-01-22T09:15:00Z", 
      last_activity: "2024-01-25T18:20:00Z",
      status: "active",
      progress: 92
    },
    {
      id: 4,
      name: "Гульнара",
      surname: "Сейтова",
      email: "<EMAIL>",
      created_at: "2024-01-21T16:45:00Z",
      last_activity: "2024-01-24T11:10:00Z", 
      status: "active",
      progress: 34
    },
    {
      id: 5,
      name: "Ерлан",
      surname: "Токтаров",
      email: "<EMAIL>",
      created_at: "2024-01-20T13:30:00Z",
      last_activity: "2024-01-22T09:45:00Z",
      status: "inactive", 
      progress: 12
    }
  ],

  words: [
    {
      id: 1,
      kaz_plaintext: "сәлем",
      rus_plaintext: "привет",
      audio_url: "http://minio:9000/klingo-audio/salem.mp3"
    },
    {
      id: 2,
      kaz_plaintext: "рахмет",
      rus_plaintext: "спасибо",
      audio_url: "http://minio:9000/klingo-audio/rahmet.mp3"
    },
    {
      id: 3,
      kaz_plaintext: "кешіріңіз",
      rus_plaintext: "извините",
      audio_url: "http://minio:9000/klingo-audio/keshiriniz.mp3"
    },
    {
      id: 4,
      kaz_plaintext: "ана",
      rus_plaintext: "мама",
      audio_url: "http://minio:9000/klingo-audio/ana.mp3"
    },
    {
      id: 5,
      kaz_plaintext: "әке",
      rus_plaintext: "папа",
      audio_url: "http://minio:9000/klingo-audio/ake.mp3"
    },
    {
      id: 6,
      kaz_plaintext: "бала",
      rus_plaintext: "ребенок",
      audio_url: "http://minio:9000/klingo-audio/bala.mp3"
    },
    {
      id: 7,
      kaz_plaintext: "үй",
      rus_plaintext: "дом",
      audio_url: "http://minio:9000/klingo-audio/uy.mp3"
    },
    {
      id: 8,
      kaz_plaintext: "су",
      rus_plaintext: "вода",
      audio_url: "http://minio:9000/klingo-audio/su.mp3"
    },
    {
      id: 9,
      kaz_plaintext: "нан",
      rus_plaintext: "хлеб",
      audio_url: "http://minio:9000/klingo-audio/nan.mp3"
    },
    {
      id: 10,
      kaz_plaintext: "кітап",
      rus_plaintext: "книга",
      audio_url: "http://minio:9000/klingo-audio/kitap.mp3"
    },
    {
      id: 11,
      kaz_plaintext: "мектеп",
      rus_plaintext: "школа",
      audio_url: "http://minio:9000/klingo-audio/mektep.mp3"
    },
    {
      id: 12,
      kaz_plaintext: "дос",
      rus_plaintext: "друг",
      audio_url: "http://minio:9000/klingo-audio/dos.mp3"
    }
  ],

  modules: [
    {
      id: 1,
      name: "Базовые приветствия",
      level: 1,
      theory_ids: [1, 2],
      question_ids: [1, 2, 3],
      pre_requisite_ids: [],
      created_at: "2024-01-01T12:00:00Z"
    },
    {
      id: 2, 
      name: "Семья и родственники",
      level: 2,
      theory_ids: [3, 4],
      question_ids: [4, 5, 6, 7],
      pre_requisite_ids: [1],
      created_at: "2024-01-02T12:00:00Z"
    }
  ],

  achievements: [
    {
      id: 1,
      name: "Первые шаги",
      description: "Завершите первый модуль",
      type: "module_completion",
      target: 1,
      created_at: "2024-01-01T12:00:00Z"
    },
    {
      id: 2,
      name: "Марафонец",
      description: "Занимайтесь 7 дней подряд",
      type: "streak", 
      target: 7,
      created_at: "2024-01-01T12:00:00Z"
    }
  ],

  systemHealth: {
    status: "available",
    system_info: {
      environment: "development",
      version: "1.0.0"
    }
  }
};

// Mock service functions
export const mockDataService = {
  async getOverviewStats() {
    await new Promise(resolve => setTimeout(resolve, 500));
    return mockStats.overview;
  },

  async getUsers(filters = {}) {
    await new Promise(resolve => setTimeout(resolve, 300));
    let users = [...mockStats.users];
    
    if (filters.search) {
      users = users.filter(user => 
        user.name.toLowerCase().includes(filters.search.toLowerCase()) ||
        user.surname.toLowerCase().includes(filters.search.toLowerCase()) ||
        user.email.toLowerCase().includes(filters.search.toLowerCase())
      );
    }
    
    if (filters.status) {
      users = users.filter(user => user.status === filters.status);
    }
    
    return {
      users,
      total: users.length,
      page: filters.page || 1,
      limit: filters.limit || 20
    };
  },

  async getWords() {
    await new Promise(resolve => setTimeout(resolve, 300));
    return mockStats.words;
  },

  async getModules() {
    await new Promise(resolve => setTimeout(resolve, 300));
    return mockStats.modules;
  },

  async getAchievements() {
    await new Promise(resolve => setTimeout(resolve, 300));
    return mockStats.achievements;
  },

  async getSystemHealth() {
    await new Promise(resolve => setTimeout(resolve, 200));
    return mockStats.systemHealth;
  },

  // Get analytics data
  async getAnalytics(type, dateRange) {
    await new Promise(resolve => setTimeout(resolve, 800));

    const generateDates = (days) => {
      const dates = [];
      for (let i = days - 1; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        dates.push(date.toISOString().split('T')[0]);
      }
      return dates;
    };

    const days = dateRange === '7d' ? 7 : dateRange === '30d' ? 30 : dateRange === '90d' ? 90 : 365;
    const dates = generateDates(days);

    if (type === 'users') {
      return {
        totalUsers: 1234,
        newUsers: 89,
        activeUsers: 856,
        userGrowth: 12.5,
        newUserGrowth: 8.3,
        activeUserGrowth: 15.2,
        userRegistrations: dates.map(date => ({
          date,
          count: Math.floor(Math.random() * 20) + 5
        })),
        userActivity: dates.slice(-7).map(date => ({
          date,
          active: Math.floor(Math.random() * 100) + 50,
          new: Math.floor(Math.random() * 20) + 5
        })),
        usersByRole: [
          { role: 'Студенты', count: 1000 },
          { role: 'Преподаватели', count: 200 },
          { role: 'Администраторы', count: 30 },
          { role: 'Модераторы', count: 4 }
        ],
        usersByStatus: [
          { status: 'Активные', count: 856 },
          { status: 'Неактивные', count: 300 },
          { status: 'Заблокированные', count: 78 }
        ]
      };
    }

    if (type === 'content') {
      return {
        totalWords: 2456,
        totalSentences: 1890,
        totalQuestions: 567,
        totalTheories: 89,
        totalModules: 23,
        contentGrowth: {
          words: 15.2,
          sentences: 12.8,
          questions: 8.5,
          theories: 22.1,
          modules: 5.3
        },
        contentUsage: [
          { name: 'Базовые приветствия', views: 1250, interactions: 890 },
          { name: 'Семья и родственники', views: 980, interactions: 720 },
          { name: 'Еда и напитки', views: 850, interactions: 650 },
          { name: 'Время и даты', views: 720, interactions: 520 },
          { name: 'Числа', views: 650, interactions: 480 }
        ],
        popularContent: [
          { title: 'Базовые приветствия', type: 'Модуль', views: 1250, likes: 89 },
          { title: 'сәлем', type: 'Слово', views: 980, likes: 76 },
          { title: 'Семейные отношения', type: 'Теория', views: 850, likes: 65 },
          { title: 'Как сказать привет?', type: 'Вопрос', views: 720, likes: 54 },
          { title: 'Менің атым...', type: 'Предложение', views: 650, likes: 43 }
        ],
        contentByType: [
          { type: 'Слова', count: 2456 },
          { type: 'Предложения', count: 1890 },
          { type: 'Вопросы', count: 567 },
          { type: 'Теории', count: 89 },
          { type: 'Модули', count: 23 }
        ],
        contentCreation: dates.slice(-30).map(date => ({
          date,
          words: Math.floor(Math.random() * 50) + 10,
          sentences: Math.floor(Math.random() * 30) + 5,
          questions: Math.floor(Math.random() * 15) + 2,
          theories: Math.floor(Math.random() * 5) + 1
        }))
      };
    }

    if (type === 'system') {
      return {
        serverMetrics: {
          cpuUsage: Math.floor(Math.random() * 40) + 20,
          memoryUsage: Math.floor(Math.random() * 30) + 40,
          activeConnections: Math.floor(Math.random() * 100) + 50
        },
        databaseMetrics: {
          size: 1024 * 1024 * 1024 * 2.5, // 2.5GB
          queriesPerSecond: Math.floor(Math.random() * 50) + 100
        },
        storageMetrics: {
          used: 1024 * 1024 * 1024 * 5.2, // 5.2GB
          free: 1024 * 1024 * 1024 * 14.8, // 14.8GB
          total: 1024 * 1024 * 1024 * 20, // 20GB
          usagePercent: 26
        },
        performanceMetrics: dates.slice(-24).map(date => ({
          time: date,
          cpu: Math.floor(Math.random() * 40) + 20,
          memory: Math.floor(Math.random() * 30) + 40,
          diskIO: Math.floor(Math.random() * 100) + 50
        })),
        errorLogs: [
          { type: '4xx', message: 'User not found', timestamp: '2024-01-15T10:30:00Z', source: 'API' },
          { type: '5xx', message: 'Database connection timeout', timestamp: '2024-01-15T09:15:00Z', source: 'Database' },
          { type: 'network', message: 'MinIO connection failed', timestamp: '2024-01-15T08:45:00Z', source: 'Storage' }
        ],
        responseTime: dates.slice(-24).map(date => ({
          time: date,
          value: Math.floor(Math.random() * 200) + 100
        })),
        uptime: 2592000 // 30 days in seconds
      };
    }

    return {};
  },

  // Get achievements
  async getAchievements(filters = {}) {
    await new Promise(resolve => setTimeout(resolve, 500));

    const mockAchievements = [
      {
        id: 1,
        name: 'Первые шаги',
        description: 'Завершите свой первый урок',
        type: 'milestone',
        category: 'learning',
        rarity: 'common',
        icon: 'star',
        points: 50,
        requirements: {
          type: 'count',
          target: 1,
          description: 'Завершите 1 урок'
        },
        isActive: true,
        createdAt: '2024-01-01T12:00:00Z'
      },
      {
        id: 2,
        name: 'Знаток слов',
        description: 'Изучите 100 новых слов',
        type: 'progress',
        category: 'learning',
        rarity: 'uncommon',
        icon: 'book',
        points: 200,
        requirements: {
          type: 'count',
          target: 100,
          description: 'Изучите 100 слов'
        },
        isActive: true,
        createdAt: '2024-01-02T12:00:00Z'
      },
      {
        id: 3,
        name: 'Серийный ученик',
        description: 'Занимайтесь 7 дней подряд',
        type: 'streak',
        category: 'time',
        rarity: 'rare',
        icon: 'zap',
        points: 500,
        requirements: {
          type: 'streak',
          target: 7,
          description: 'Занимайтесь 7 дней подряд'
        },
        isActive: true,
        createdAt: '2024-01-03T12:00:00Z'
      },
      {
        id: 4,
        name: 'Мастер языка',
        description: 'Завершите все модули',
        type: 'collection',
        category: 'progress',
        rarity: 'epic',
        icon: 'award',
        points: 1000,
        requirements: {
          type: 'percentage',
          target: 100,
          description: 'Завершите 100% модулей'
        },
        isActive: true,
        createdAt: '2024-01-04T12:00:00Z'
      },
      {
        id: 5,
        name: 'Легенда',
        description: 'Получите все остальные достижения',
        type: 'special',
        category: 'special',
        rarity: 'legendary',
        icon: 'shield',
        points: 2000,
        requirements: {
          type: 'count',
          target: 50,
          description: 'Получите все остальные достижения'
        },
        isActive: true,
        createdAt: '2024-01-05T12:00:00Z'
      }
    ];

    let filtered = [...mockAchievements];

    if (filters.search) {
      filtered = filtered.filter(achievement =>
        achievement.name.toLowerCase().includes(filters.search.toLowerCase()) ||
        achievement.description.toLowerCase().includes(filters.search.toLowerCase())
      );
    }

    if (filters.category) {
      filtered = filtered.filter(achievement => achievement.category === filters.category);
    }

    if (filters.rarity) {
      filtered = filtered.filter(achievement => achievement.rarity === filters.rarity);
    }

    return filtered;
  },

  // Get user progress
  async getUserProgress() {
    await new Promise(resolve => setTimeout(resolve, 600));

    return [
      {
        user: {
          id: 1,
          name: 'Айдар Нурланов',
          email: '<EMAIL>',
          avatar: null,
          createdAt: '2024-01-01T12:00:00Z'
        },
        totalPoints: 1250,
        totalAchievements: 5,
        unlockedAchievements: 3,
        level: 8,
        experiencePoints: 650,
        nextLevelPoints: 1000,
        streak: 12,
        lastActivity: '2024-01-15T10:30:00Z',
        recentAchievements: [
          {
            id: 1,
            name: 'Первые шаги',
            points: 50,
            unlockedAt: '2024-01-10T12:00:00Z'
          },
          {
            id: 2,
            name: 'Знаток слов',
            points: 200,
            unlockedAt: '2024-01-12T15:30:00Z'
          }
        ],
        categoryProgress: {
          learning: { unlocked: 2, total: 3, percentage: 67 },
          time: { unlocked: 1, total: 1, percentage: 100 },
          progress: { unlocked: 0, total: 1, percentage: 0 },
          special: { unlocked: 0, total: 1, percentage: 0 }
        }
      },
      {
        user: {
          id: 2,
          name: 'Асель Қасымова',
          email: '<EMAIL>',
          avatar: null,
          createdAt: '2024-01-02T12:00:00Z'
        },
        totalPoints: 850,
        totalAchievements: 5,
        unlockedAchievements: 2,
        level: 5,
        experiencePoints: 350,
        nextLevelPoints: 500,
        streak: 5,
        lastActivity: '2024-01-14T18:45:00Z',
        recentAchievements: [
          {
            id: 1,
            name: 'Первые шаги',
            points: 50,
            unlockedAt: '2024-01-08T14:20:00Z'
          }
        ],
        categoryProgress: {
          learning: { unlocked: 1, total: 3, percentage: 33 },
          time: { unlocked: 0, total: 1, percentage: 0 },
          progress: { unlocked: 0, total: 1, percentage: 0 },
          special: { unlocked: 0, total: 1, percentage: 0 }
        }
      }
    ];
  },

  // Get achievement stats
  async getAchievementStats() {
    await new Promise(resolve => setTimeout(resolve, 300));

    return {
      totalAchievements: 5,
      activeUsers: 156,
      totalUnlocked: 89,
      averageProgress: 42
    };
  }
};

export default mockDataService;
