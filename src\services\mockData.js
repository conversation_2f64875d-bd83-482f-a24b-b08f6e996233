// Mock data for development and testing

export const mockStats = {
  overview: {
    total_users: 1247,
    active_users_today: 89,
    active_users_week: 456,
    total_modules: 24,
    total_questions: 450,
    total_words: 3456,
    avg_completion_rate: 67.8,
    popular_modules: [
      { id: 1, name: "Базовые приветствия", users_count: 234 },
      { id: 2, name: "Семья и родственники", users_count: 189 },
      { id: 3, name: "Еда и напитки", users_count: 156 },
      { id: 4, name: "Время и даты", users_count: 134 },
      { id: 5, name: "Цвета и формы", users_count: 112 }
    ]
  },
  
  users: [
    {
      id: 1,
      name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      surname: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", 
      email: "<EMAIL>",
      created_at: "2024-01-24T10:30:00Z",
      last_activity: "2024-01-25T15:45:00Z",
      status: "active",
      progress: 85
    },
    {
      id: 2,
      name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      surname: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
      email: "<EMAIL>", 
      created_at: "2024-01-23T14:20:00Z",
      last_activity: "2024-01-25T12:30:00Z",
      status: "active",
      progress: 67
    },
    {
      id: 3,
      name: "Данияр",
      surname: "Касымов",
      email: "<EMAIL>",
      created_at: "2024-01-22T09:15:00Z", 
      last_activity: "2024-01-25T18:20:00Z",
      status: "active",
      progress: 92
    },
    {
      id: 4,
      name: "Гульнара",
      surname: "Сейтова",
      email: "<EMAIL>",
      created_at: "2024-01-21T16:45:00Z",
      last_activity: "2024-01-24T11:10:00Z", 
      status: "active",
      progress: 34
    },
    {
      id: 5,
      name: "Ерлан",
      surname: "Токтаров",
      email: "<EMAIL>",
      created_at: "2024-01-20T13:30:00Z",
      last_activity: "2024-01-22T09:45:00Z",
      status: "inactive", 
      progress: 12
    }
  ],

  words: [
    {
      id: 1,
      kaz_plaintext: "сәлем",
      rus_plaintext: "привет",
      audio_url: "http://minio:9000/klingo-audio/salem.mp3"
    },
    {
      id: 2,
      kaz_plaintext: "рахмет",
      rus_plaintext: "спасибо", 
      audio_url: "http://minio:9000/klingo-audio/rahmet.mp3"
    },
    {
      id: 3,
      kaz_plaintext: "кешіріңіз",
      rus_plaintext: "извините",
      audio_url: "http://minio:9000/klingo-audio/keshiriniz.mp3"
    }
  ],

  modules: [
    {
      id: 1,
      name: "Базовые приветствия",
      level: 1,
      theory_ids: [1, 2],
      question_ids: [1, 2, 3],
      pre_requisite_ids: [],
      created_at: "2024-01-01T12:00:00Z"
    },
    {
      id: 2, 
      name: "Семья и родственники",
      level: 2,
      theory_ids: [3, 4],
      question_ids: [4, 5, 6, 7],
      pre_requisite_ids: [1],
      created_at: "2024-01-02T12:00:00Z"
    }
  ],

  achievements: [
    {
      id: 1,
      name: "Первые шаги",
      description: "Завершите первый модуль",
      type: "module_completion",
      target: 1,
      created_at: "2024-01-01T12:00:00Z"
    },
    {
      id: 2,
      name: "Марафонец",
      description: "Занимайтесь 7 дней подряд",
      type: "streak", 
      target: 7,
      created_at: "2024-01-01T12:00:00Z"
    }
  ],

  systemHealth: {
    status: "available",
    system_info: {
      environment: "development",
      version: "1.0.0"
    }
  }
};

// Mock service functions
export const mockDataService = {
  async getOverviewStats() {
    await new Promise(resolve => setTimeout(resolve, 500));
    return mockStats.overview;
  },

  async getUsers(filters = {}) {
    await new Promise(resolve => setTimeout(resolve, 300));
    let users = [...mockStats.users];
    
    if (filters.search) {
      users = users.filter(user => 
        user.name.toLowerCase().includes(filters.search.toLowerCase()) ||
        user.surname.toLowerCase().includes(filters.search.toLowerCase()) ||
        user.email.toLowerCase().includes(filters.search.toLowerCase())
      );
    }
    
    if (filters.status) {
      users = users.filter(user => user.status === filters.status);
    }
    
    return {
      users,
      total: users.length,
      page: filters.page || 1,
      limit: filters.limit || 20
    };
  },

  async getWords() {
    await new Promise(resolve => setTimeout(resolve, 300));
    return mockStats.words;
  },

  async getModules() {
    await new Promise(resolve => setTimeout(resolve, 300));
    return mockStats.modules;
  },

  async getAchievements() {
    await new Promise(resolve => setTimeout(resolve, 300));
    return mockStats.achievements;
  },

  async getSystemHealth() {
    await new Promise(resolve => setTimeout(resolve, 200));
    return mockStats.systemHealth;
  }
};

export default mockDataService;
