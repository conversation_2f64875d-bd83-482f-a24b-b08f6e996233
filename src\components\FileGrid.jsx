import React, { useState } from 'react';
import { 
  FiFile, 
  FiImage, 
  FiMusic, 
  FiVideo, 
  FiDownload, 
  FiTrash2, 
  FiEye,
  FiMoreVertical,
  FiCopy,
  FiExternalLink
} from 'react-icons/fi';
import { formatFileSize, formatDate, isAudioFile, isImageFile } from '../utils/helpers';

const FileItem = ({ file, onPreview, onDownload, onDelete, onCopyUrl }) => {
  const [showMenu, setShowMenu] = useState(false);

  const getFileIcon = () => {
    if (isImageFile(file.name)) {
      return <FiImage className="text-blue-600" size={24} />;
    }
    if (isAudioFile(file.name)) {
      return <FiMusic className="text-green-600" size={24} />;
    }
    if (file.name.toLowerCase().includes('video')) {
      return <FiVideo className="text-purple-600" size={24} />;
    }
    return <FiFile className="text-gray-600" size={24} />;
  };

  const canPreview = () => {
    return isImageFile(file.name) || isAudioFile(file.name);
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow relative">
      {/* File icon/thumbnail */}
      <div className="flex items-center justify-center h-16 mb-3">
        {isImageFile(file.name) ? (
          <img 
            src={file.url} 
            alt={file.name}
            className="max-h-16 max-w-full object-cover rounded"
            onError={(e) => {
              e.target.style.display = 'none';
              e.target.nextSibling.style.display = 'block';
            }}
          />
        ) : null}
        <div style={{ display: isImageFile(file.name) ? 'none' : 'block' }}>
          {getFileIcon()}
        </div>
      </div>

      {/* File info */}
      <div className="text-center">
        <h4 className="font-medium text-sm text-gray-900 truncate" title={file.name}>
          {file.name}
        </h4>
        <p className="text-xs text-gray-500 mt-1">
          {formatFileSize(file.size)}
        </p>
        <p className="text-xs text-gray-400">
          {formatDate(file.lastModified)}
        </p>
      </div>

      {/* Actions menu */}
      <div className="absolute top-2 right-2">
        <button
          onClick={() => setShowMenu(!showMenu)}
          className="p-1 rounded hover:bg-gray-100"
        >
          <FiMoreVertical size={16} />
        </button>

        {showMenu && (
          <div className="absolute right-0 top-8 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-10">
            {canPreview() && (
              <button
                onClick={() => {
                  onPreview(file);
                  setShowMenu(false);
                }}
                className="flex items-center gap-2 w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
              >
                <FiEye size={14} />
                Предварительный просмотр
              </button>
            )}
            <button
              onClick={() => {
                onDownload(file);
                setShowMenu(false);
              }}
              className="flex items-center gap-2 w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
            >
              <FiDownload size={14} />
              Скачать
            </button>
            <button
              onClick={() => {
                onCopyUrl(file);
                setShowMenu(false);
              }}
              className="flex items-center gap-2 w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
            >
              <FiCopy size={14} />
              Копировать URL
            </button>
            <button
              onClick={() => {
                window.open(file.url, '_blank');
                setShowMenu(false);
              }}
              className="flex items-center gap-2 w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
            >
              <FiExternalLink size={14} />
              Открыть в новой вкладке
            </button>
            <hr className="my-1" />
            <button
              onClick={() => {
                onDelete(file);
                setShowMenu(false);
              }}
              className="flex items-center gap-2 w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50"
            >
              <FiTrash2 size={14} />
              Удалить
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

const FileGrid = ({ 
  files = [], 
  loading = false, 
  onPreview, 
  onDownload, 
  onDelete, 
  onCopyUrl 
}) => {
  // Close all menus when clicking outside
  React.useEffect(() => {
    const handleClickOutside = () => {
      // This will be handled by individual FileItem components
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  if (loading) {
    return (
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
        {[...Array(12)].map((_, index) => (
          <div key={index} className="bg-gray-200 rounded-lg p-4 animate-pulse">
            <div className="h-16 bg-gray-300 rounded mb-3"></div>
            <div className="h-4 bg-gray-300 rounded mb-2"></div>
            <div className="h-3 bg-gray-300 rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  if (files.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-gray-500">
        <FiFile size={48} className="mb-4 text-gray-300" />
        <p className="text-lg font-medium">Файлы не найдены</p>
        <p className="text-sm">Загрузите файлы или измените фильтры поиска</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
      {files.map((file, index) => (
        <FileItem
          key={`${file.name}-${index}`}
          file={file}
          onPreview={onPreview}
          onDownload={onDownload}
          onDelete={onDelete}
          onCopyUrl={onCopyUrl}
        />
      ))}
    </div>
  );
};

export default FileGrid;
