import React, { useState, useRef } from 'react';
import { FiUpload, FiX, FiFile, FiImage, FiMusic, FiCheck, FiAlertCircle } from 'react-icons/fi';
import { formatFileSize, isAudioFile, isImageFile } from '../utils/helpers';
import { FILE_SIZE_LIMITS, SUPPORTED_FORMATS } from '../utils/constants';

const FileUpload = ({ onUpload, accept = "*/*", multiple = true, maxSize = FILE_SIZE_LIMITS.MULTIPLE }) => {
  const [dragActive, setDragActive] = useState(false);
  const [files, setFiles] = useState([]);
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef(null);

  const validateFile = (file) => {
    const errors = [];
    
    // Check file size
    if (file.size > maxSize * 1024 * 1024) {
      errors.push(`Файл слишком большой (максимум ${maxSize}MB)`);
    }
    
    // Check file type if specific accept is provided
    if (accept !== "*/*") {
      const extension = file.name.split('.').pop().toLowerCase();
      if (accept.includes('image/*') && !isImageFile(file.name)) {
        errors.push('Неподдерживаемый формат изображения');
      }
      if (accept.includes('audio/*') && !isAudioFile(file.name)) {
        errors.push('Неподдерживаемый формат аудио');
      }
    }
    
    return errors;
  };

  const handleFiles = (fileList) => {
    const newFiles = Array.from(fileList).map(file => {
      const errors = validateFile(file);
      return {
        file,
        id: Math.random().toString(36).substr(2, 9),
        name: file.name,
        size: file.size,
        type: file.type,
        errors,
        status: errors.length > 0 ? 'error' : 'pending'
      };
    });

    if (multiple) {
      setFiles(prev => [...prev, ...newFiles]);
    } else {
      setFiles(newFiles.slice(0, 1));
    }
  };

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  };

  const handleChange = (e) => {
    e.preventDefault();
    if (e.target.files && e.target.files[0]) {
      handleFiles(e.target.files);
    }
  };

  const removeFile = (fileId) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const handleUpload = async () => {
    const validFiles = files.filter(f => f.status === 'pending');
    if (validFiles.length === 0) return;

    setUploading(true);
    
    try {
      for (const fileItem of validFiles) {
        fileItem.status = 'uploading';
        setFiles(prev => [...prev]);
        
        try {
          await onUpload(fileItem.file);
          fileItem.status = 'success';
        } catch (error) {
          fileItem.status = 'error';
          fileItem.errors = [error.message || 'Ошибка загрузки'];
        }
        
        setFiles(prev => [...prev]);
      }
    } finally {
      setUploading(false);
      
      // Remove successful uploads after a delay
      setTimeout(() => {
        setFiles(prev => prev.filter(f => f.status !== 'success'));
      }, 2000);
    }
  };

  const getFileIcon = (file) => {
    if (isImageFile(file.name)) {
      return <FiImage className="text-blue-600" size={20} />;
    }
    if (isAudioFile(file.name)) {
      return <FiMusic className="text-green-600" size={20} />;
    }
    return <FiFile className="text-gray-600" size={20} />;
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success':
        return <FiCheck className="text-green-600" size={16} />;
      case 'error':
        return <FiAlertCircle className="text-red-600" size={16} />;
      case 'uploading':
        return <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />;
      default:
        return null;
    }
  };

  const validFilesCount = files.filter(f => f.status === 'pending').length;

  return (
    <div className="w-full">
      {/* Drop zone */}
      <div
        className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          dragActive 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple={multiple}
          accept={accept}
          onChange={handleChange}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
        />
        
        <FiUpload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <p className="text-lg font-medium text-gray-900 mb-2">
          Перетащите файлы сюда или нажмите для выбора
        </p>
        <p className="text-sm text-gray-500">
          {accept === "*/*" 
            ? `Максимальный размер файла: ${maxSize}MB`
            : `Поддерживаемые форматы: ${accept.replace(/\*/g, 'все')} (до ${maxSize}MB)`
          }
        </p>
      </div>

      {/* File list */}
      {files.length > 0 && (
        <div className="mt-4">
          <div className="flex items-center justify-between mb-3">
            <h4 className="font-medium text-gray-900">
              Файлы для загрузки ({files.length})
            </h4>
            {validFilesCount > 0 && (
              <button
                onClick={handleUpload}
                disabled={uploading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {uploading ? 'Загрузка...' : `Загрузить (${validFilesCount})`}
              </button>
            )}
          </div>
          
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {files.map((fileItem) => (
              <div
                key={fileItem.id}
                className={`flex items-center gap-3 p-3 border rounded-lg ${
                  fileItem.status === 'error' 
                    ? 'border-red-200 bg-red-50' 
                    : fileItem.status === 'success'
                    ? 'border-green-200 bg-green-50'
                    : 'border-gray-200 bg-white'
                }`}
              >
                {getFileIcon(fileItem)}
                
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {fileItem.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatFileSize(fileItem.size)}
                  </p>
                  {fileItem.errors.length > 0 && (
                    <p className="text-xs text-red-600 mt-1">
                      {fileItem.errors.join(', ')}
                    </p>
                  )}
                </div>
                
                <div className="flex items-center gap-2">
                  {getStatusIcon(fileItem.status)}
                  
                  {fileItem.status === 'pending' && (
                    <button
                      onClick={() => removeFile(fileItem.id)}
                      className="p-1 text-gray-400 hover:text-gray-600"
                    >
                      <FiX size={16} />
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default FileUpload;
