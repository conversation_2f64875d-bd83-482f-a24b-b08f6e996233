import React, { useState, useEffect } from 'react';
import { FiX, FiPlus, FiTrash2, FiEye, FiUpload, FiPlay, FiPause } from 'react-icons/fi';
import { useForm, useFieldArray } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { QUESTION_TYPES } from '../utils/constants';

// Validation schema
const schema = yup.object({
  type: yup
    .string()
    .required('Тип вопроса обязателен')
    .oneOf(Object.values(QUESTION_TYPES)),
  question_text: yup
    .string()
    .required('Текст вопроса обязателен')
    .min(1, 'Минимум 1 символ'),
  correct_answer: yup
    .string()
    .required('Правильный ответ обязателен'),
  options: yup
    .array()
    .of(yup.string().required('Вариант ответа не может быть пустым'))
    .min(2, 'Минимум 2 варианта ответа'),
  word_ids: yup
    .array()
    .of(yup.number())
    .min(1, 'Выберите хотя бы одно слово'),
});

const QuestionModal = ({ question, isOpen, onClose, onSave, isLoading, words = [] }) => {
  const [previewMode, setPreviewMode] = useState(false);
  const [audioFile, setAudioFile] = useState(null);
  const [audioUrl, setAudioUrl] = useState('');
  const [imageFile, setImageFile] = useState(null);
  const [imageUrl, setImageUrl] = useState('');

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    control,
    watch
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      type: QUESTION_TYPES.TRANSLATION,
      options: ['', ''],
      word_ids: []
    }
  });

  const { fields: optionFields, append: appendOption, remove: removeOption } = useFieldArray({
    control,
    name: 'options'
  });

  const { fields: wordFields, append: appendWord, remove: removeWord } = useFieldArray({
    control,
    name: 'word_ids'
  });

  const watchedType = watch('type');
  const watchedOptions = watch('options');
  const watchedQuestionText = watch('question_text');
  const watchedCorrectAnswer = watch('correct_answer');

  useEffect(() => {
    if (isOpen && question) {
      // Edit mode - populate form
      setValue('type', question.type || QUESTION_TYPES.TRANSLATION);
      setValue('question_text', question.question_text || '');
      setValue('correct_answer', question.correct_answer || '');
      setValue('options', question.options || ['', '']);
      setValue('word_ids', question.word_ids || []);
      setAudioUrl(question.audio_url || '');
      setImageUrl(question.image_url || '');
    } else if (isOpen) {
      // Create mode - reset form
      reset();
      setAudioUrl('');
      setImageUrl('');
      setAudioFile(null);
      setImageFile(null);
    }
  }, [isOpen, question, setValue, reset]);

  const handleAudioFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      if (file.type.startsWith('audio/')) {
        setAudioFile(file);
        const url = URL.createObjectURL(file);
        setAudioUrl(url);
      } else {
        alert('Пожалуйста, выберите аудио файл');
      }
    }
  };

  const handleImageFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      if (file.type.startsWith('image/')) {
        setImageFile(file);
        const url = URL.createObjectURL(file);
        setImageUrl(url);
      } else {
        alert('Пожалуйста, выберите изображение');
      }
    }
  };

  const handleAddOption = () => {
    appendOption('');
  };

  const handleAddWord = () => {
    appendWord(0);
  };

  const getSelectedWords = () => {
    const selectedWordIds = watch('word_ids') || [];
    return selectedWordIds
      .map(id => words.find(word => word.id === id))
      .filter(Boolean);
  };

  const onSubmit = (data) => {
    const questionData = {
      ...data,
      word_ids: data.word_ids.filter(id => id > 0),
      options: data.options.filter(option => option.trim() !== ''),
      audio_file: audioFile,
      image_file: imageFile,
      audio_url: audioUrl && !audioFile ? audioUrl : null,
      image_url: imageUrl && !imageFile ? imageUrl : null,
    };

    onSave(questionData);
  };

  const renderQuestionPreview = () => {
    const selectedWords = getSelectedWords();
    
    return (
      <div className="bg-gray-50 p-4 rounded-lg">
        <h4 className="font-medium text-gray-900 mb-3">Предварительный просмотр</h4>
        
        {/* Question display */}
        <div className="bg-white p-4 rounded-md border">
          <div className="mb-4">
            <h5 className="font-medium text-lg mb-2">{watchedQuestionText || 'Текст вопроса'}</h5>
            
            {/* Show related words */}
            {selectedWords.length > 0 && (
              <div className="mb-3">
                <span className="text-sm text-gray-600">Связанные слова: </span>
                {selectedWords.map((word, index) => (
                  <span key={word.id} className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded mr-1">
                    {word.kaz_plaintext} - {word.rus_plaintext}
                  </span>
                ))}
              </div>
            )}

            {/* Media preview */}
            {imageUrl && (
              <div className="mb-3">
                <img src={imageUrl} alt="Question" className="max-w-xs max-h-32 object-cover rounded" />
              </div>
            )}

            {audioUrl && (
              <div className="mb-3">
                <audio controls className="w-full max-w-xs">
                  <source src={audioUrl} type="audio/mpeg" />
                  Ваш браузер не поддерживает аудио элемент.
                </audio>
              </div>
            )}
          </div>

          {/* Answer options */}
          <div className="space-y-2">
            {watchedOptions?.filter(option => option.trim() !== '').map((option, index) => (
              <label key={index} className="flex items-center space-x-2 cursor-pointer">
                <input 
                  type="radio" 
                  name="preview_answer" 
                  value={option}
                  checked={option === watchedCorrectAnswer}
                  readOnly
                  className="text-blue-600"
                />
                <span className={option === watchedCorrectAnswer ? 'font-medium text-green-600' : ''}>
                  {option}
                </span>
              </label>
            ))}
          </div>
        </div>
      </div>
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {question ? 'Редактировать вопрос' : 'Создать вопрос'}
          </h2>
          <div className="flex items-center gap-2">
            <button
              type="button"
              onClick={() => setPreviewMode(!previewMode)}
              className="flex items-center gap-2 px-3 py-1 text-blue-600 hover:text-blue-800 border border-blue-300 rounded-md hover:bg-blue-50"
            >
              <FiEye size={16} />
              {previewMode ? 'Редактор' : 'Предварительный просмотр'}
            </button>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <FiX size={24} />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {previewMode ? (
            renderQuestionPreview()
          ) : (
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Left column - Basic info */}
                <div className="space-y-4">
                  {/* Question type */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Тип вопроса *
                    </label>
                    <select
                      {...register('type')}
                      className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.type ? 'border-red-300' : 'border-gray-300'
                      }`}
                    >
                      <option value={QUESTION_TYPES.TRANSLATION}>Перевод</option>
                      <option value={QUESTION_TYPES.AUDIO}>Аудио вопрос</option>
                      <option value={QUESTION_TYPES.IMAGE}>Изображение</option>
                      <option value={QUESTION_TYPES.SENTENCE_CONSTRUCTION}>Конструирование предложения</option>
                    </select>
                    {errors.type && (
                      <p className="mt-1 text-sm text-red-600">{errors.type.message}</p>
                    )}
                  </div>

                  {/* Question text */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Текст вопроса *
                    </label>
                    <textarea
                      {...register('question_text')}
                      rows={3}
                      className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.question_text ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="Введите текст вопроса"
                    />
                    {errors.question_text && (
                      <p className="mt-1 text-sm text-red-600">{errors.question_text.message}</p>
                    )}
                  </div>

                  {/* Correct answer */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Правильный ответ *
                    </label>
                    <input
                      {...register('correct_answer')}
                      type="text"
                      className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.correct_answer ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="Введите правильный ответ"
                    />
                    {errors.correct_answer && (
                      <p className="mt-1 text-sm text-red-600">{errors.correct_answer.message}</p>
                    )}
                  </div>
                </div>

                {/* Right column - Options and media */}
                <div className="space-y-4">
                  {/* Answer options */}
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <label className="block text-sm font-medium text-gray-700">
                        Варианты ответов *
                      </label>
                      <button
                        type="button"
                        onClick={handleAddOption}
                        className="flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800"
                      >
                        <FiPlus size={14} />
                        Добавить вариант
                      </button>
                    </div>

                    {optionFields.map((field, index) => (
                      <div key={field.id} className="flex items-center gap-2 mb-2">
                        <input
                          {...register(`options.${index}`)}
                          type="text"
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder={`Вариант ${index + 1}`}
                        />
                        {optionFields.length > 2 && (
                          <button
                            type="button"
                            onClick={() => removeOption(index)}
                            className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded"
                          >
                            <FiTrash2 size={16} />
                          </button>
                        )}
                      </div>
                    ))}

                    {errors.options && (
                      <p className="mt-1 text-sm text-red-600">{errors.options.message}</p>
                    )}
                  </div>

                  {/* Media uploads */}
                  {(watchedType === QUESTION_TYPES.AUDIO || watchedType === QUESTION_TYPES.IMAGE) && (
                    <div className="space-y-3">
                      {/* Audio upload for audio questions */}
                      {watchedType === QUESTION_TYPES.AUDIO && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Аудио файл
                          </label>
                          {audioUrl ? (
                            <div className="p-3 bg-gray-50 rounded-md">
                              <audio controls className="w-full">
                                <source src={audioUrl} type="audio/mpeg" />
                              </audio>
                              <button
                                type="button"
                                onClick={() => {
                                  setAudioFile(null);
                                  setAudioUrl('');
                                }}
                                className="mt-2 text-sm text-red-600 hover:text-red-800"
                              >
                                Удалить аудио
                              </button>
                            </div>
                          ) : (
                            <div className="border-2 border-dashed border-gray-300 rounded-md p-4 text-center">
                              <FiUpload className="mx-auto h-6 w-6 text-gray-400 mb-2" />
                              <label className="cursor-pointer">
                                <span className="text-blue-600 hover:text-blue-800">
                                  Выберите аудио файл
                                </span>
                                <input
                                  type="file"
                                  accept="audio/*"
                                  onChange={handleAudioFileChange}
                                  className="hidden"
                                />
                              </label>
                            </div>
                          )}
                        </div>
                      )}

                      {/* Image upload for image questions */}
                      {watchedType === QUESTION_TYPES.IMAGE && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Изображение
                          </label>
                          {imageUrl ? (
                            <div className="p-3 bg-gray-50 rounded-md">
                              <img src={imageUrl} alt="Question" className="max-w-full max-h-32 object-cover rounded mb-2" />
                              <button
                                type="button"
                                onClick={() => {
                                  setImageFile(null);
                                  setImageUrl('');
                                }}
                                className="text-sm text-red-600 hover:text-red-800"
                              >
                                Удалить изображение
                              </button>
                            </div>
                          ) : (
                            <div className="border-2 border-dashed border-gray-300 rounded-md p-4 text-center">
                              <FiUpload className="mx-auto h-6 w-6 text-gray-400 mb-2" />
                              <label className="cursor-pointer">
                                <span className="text-blue-600 hover:text-blue-800">
                                  Выберите изображение
                                </span>
                                <input
                                  type="file"
                                  accept="image/*"
                                  onChange={handleImageFileChange}
                                  className="hidden"
                                />
                              </label>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* Related words */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Связанные слова *
                  </label>
                  <button
                    type="button"
                    onClick={handleAddWord}
                    className="flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800"
                  >
                    <FiPlus size={14} />
                    Добавить слово
                  </button>
                </div>

                {wordFields.length === 0 && (
                  <button
                    type="button"
                    onClick={handleAddWord}
                    className="w-full p-3 border-2 border-dashed border-gray-300 rounded-md text-gray-500 hover:border-gray-400 hover:text-gray-600"
                  >
                    Выберите слова для этого вопроса
                  </button>
                )}

                {wordFields.map((field, index) => (
                  <div key={field.id} className="flex items-center gap-2 mb-2">
                    <select
                      {...register(`word_ids.${index}`, { valueAsNumber: true })}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value={0}>Выберите слово</option>
                      {words.map(word => (
                        <option key={word.id} value={word.id}>
                          {word.kaz_plaintext} - {word.rus_plaintext}
                        </option>
                      ))}
                    </select>
                    <button
                      type="button"
                      onClick={() => removeWord(index)}
                      className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded"
                    >
                      <FiTrash2 size={16} />
                    </button>
                  </div>
                ))}

                {errors.word_ids && (
                  <p className="mt-1 text-sm text-red-600">{errors.word_ids.message}</p>
                )}
              </div>

              {/* Footer */}
              <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Отмена
                </button>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Сохранение...' : 'Сохранить'}
                </button>
              </div>
            </form>
          )}

          {/* Preview mode footer */}
          {previewMode && (
            <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Закрыть
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default QuestionModal;
