import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, FiE<PERSON>2, <PERSON><PERSON><PERSON>2, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FiTag, FiFileText } from 'react-icons/fi';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import TheoryModal from '../components/TheoryModal';
import Table from '../components/Table';
import { mockDataService } from '../services/mockData';
import contentService from '../services/content';
import { debounce, truncateText, formatDate } from '../utils/helpers';

// Use mock service in development mode
const USE_MOCK = import.meta.env.DEV;

// Mock theories data
const mockTheories = [
  {
    id: 1,
    name: 'Базовые приветствия',
    content: '<h2>Приветствие в казахском языке</h2><p>В казахском языке существует несколько способов поприветствовать собеседника...</p>',
    tags: ['приветствие', 'базовый', 'общение'],
    sentence_ids: [1, 2],
    created_at: '2024-01-01T12:00:00Z'
  },
  {
    id: 2,
    name: 'Семья и родственники',
    content: '<h2>Семейные отношения</h2><p>Семья играет важную роль в казахской культуре. Рассмотрим основные термины...</p>',
    tags: ['семья', 'родственники', 'культура'],
    sentence_ids: [3],
    created_at: '2024-01-02T12:00:00Z'
  },
  {
    id: 3,
    name: 'Еда и напитки',
    content: '<h2>Казахская кухня</h2><p>Традиционная казахская кухня богата и разнообразна...</p>',
    tags: ['еда', 'кухня', 'традиции'],
    sentence_ids: [],
    created_at: '2024-01-03T12:00:00Z'
  },
];

const Theories = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTheory, setSelectedTheory] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [previewTheory, setPreviewTheory] = useState(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  const queryClient = useQueryClient();

  // Fetch theories
  const { data: theories = [], isLoading, error, refetch } = useQuery({
    queryKey: ['theories', searchTerm],
    queryFn: async () => {
      if (USE_MOCK) {
        let filteredTheories = [...mockTheories];
        if (searchTerm) {
          filteredTheories = filteredTheories.filter(theory => 
            theory.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            theory.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
          );
        }
        return filteredTheories;
      } else {
        return await contentService.theories.getAll();
      }
    },
    staleTime: 30 * 1000,
  });

  // Fetch sentences for modal
  const { data: sentences = [] } = useQuery({
    queryKey: ['sentences'],
    queryFn: async () => {
      if (USE_MOCK) {
        return [
          { id: 1, kaz_plaintext: 'Менің атым Айдар.', rus_plaintext: 'Меня зовут Айдар.' },
          { id: 2, kaz_plaintext: 'Сіз қалай жақсысыз?', rus_plaintext: 'Как дела?' },
          { id: 3, kaz_plaintext: 'Менің анам үйде.', rus_plaintext: 'Моя мама дома.' },
        ];
      } else {
        return await contentService.sentences.getAll();
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Create theory mutation
  const createTheoryMutation = useMutation({
    mutationFn: async (theoryData) => {
      if (USE_MOCK) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        return { id: Date.now(), ...theoryData, created_at: new Date().toISOString() };
      } else {
        return await contentService.theories.create(theoryData);
      }
    },
    onSuccess: () => {
      toast.success('Теория успешно создана');
      queryClient.invalidateQueries(['theories']);
      setIsModalOpen(false);
      setSelectedTheory(null);
    },
    onError: (error) => {
      toast.error('Ошибка при создании теории');
      console.error('Create theory error:', error);
    }
  });

  // Update theory mutation
  const updateTheoryMutation = useMutation({
    mutationFn: async ({ id, theoryData }) => {
      if (USE_MOCK) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        return { id, ...theoryData };
      } else {
        return await contentService.theories.update(id, theoryData);
      }
    },
    onSuccess: () => {
      toast.success('Теория успешно обновлена');
      queryClient.invalidateQueries(['theories']);
      setIsModalOpen(false);
      setSelectedTheory(null);
    },
    onError: (error) => {
      toast.error('Ошибка при обновлении теории');
      console.error('Update theory error:', error);
    }
  });

  // Delete theory mutation
  const deleteTheoryMutation = useMutation({
    mutationFn: async (theoryId) => {
      if (USE_MOCK) {
        await new Promise(resolve => setTimeout(resolve, 500));
        return { success: true };
      } else {
        return await contentService.theories.delete(theoryId);
      }
    },
    onSuccess: () => {
      toast.success('Теория успешно удалена');
      queryClient.invalidateQueries(['theories']);
    },
    onError: (error) => {
      toast.error('Ошибка при удалении теории');
      console.error('Delete theory error:', error);
    }
  });

  // Debounced search
  const debouncedSearch = debounce((value) => {
    setSearchTerm(value);
  }, 300);

  const handleSearch = (e) => {
    debouncedSearch(e.target.value);
  };

  const handleCreateTheory = () => {
    setSelectedTheory(null);
    setIsModalOpen(true);
  };

  const handleEditTheory = (theory) => {
    setSelectedTheory(theory);
    setIsModalOpen(true);
  };

  const handleDeleteTheory = (theory) => {
    if (window.confirm(`Вы уверены, что хотите удалить теорию "${theory.name}"?`)) {
      deleteTheoryMutation.mutate(theory.id);
    }
  };

  const handlePreviewTheory = (theory) => {
    setPreviewTheory(theory);
    setIsPreviewOpen(true);
  };

  const handleSaveTheory = (theoryData) => {
    if (selectedTheory) {
      updateTheoryMutation.mutate({ id: selectedTheory.id, theoryData });
    } else {
      createTheoryMutation.mutate(theoryData);
    }
  };

  const getRelatedSentences = (sentenceIds) => {
    if (!sentenceIds || sentenceIds.length === 0) return [];
    return sentenceIds
      .map(id => sentences.find(sentence => sentence.id === id))
      .filter(Boolean);
  };

  // Table columns
  const columns = [
    {
      header: 'Название',
      accessor: 'name',
      render: (row) => (
        <div>
          <div className="font-medium text-gray-900">{row.name}</div>
          <div className="text-sm text-gray-500">{formatDate(row.created_at)}</div>
        </div>
      )
    },
    {
      header: 'Содержание',
      accessor: 'content',
      render: (row) => (
        <div className="max-w-xs">
          <div 
            className="text-sm text-gray-700"
            dangerouslySetInnerHTML={{ 
              __html: truncateText(row.content.replace(/<[^>]*>/g, ''), 100) 
            }}
          />
        </div>
      )
    },
    {
      header: 'Теги',
      accessor: 'tags',
      render: (row) => (
        <div className="flex flex-wrap gap-1">
          {row.tags?.slice(0, 3).map((tag, index) => (
            <span 
              key={index}
              className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
            >
              <FiTag size={10} />
              {tag}
            </span>
          ))}
          {row.tags?.length > 3 && (
            <span className="text-xs text-gray-500">+{row.tags.length - 3}</span>
          )}
        </div>
      )
    },
    {
      header: 'Примеры',
      accessor: 'sentence_ids',
      render: (row) => {
        const relatedSentences = getRelatedSentences(row.sentence_ids);
        return (
          <div className="text-sm text-gray-600">
            {relatedSentences.length} предложений
          </div>
        );
      }
    },
    {
      header: 'Действия',
      accessor: 'actions',
      render: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handlePreviewTheory(row)}
            className="p-1 text-green-600 hover:text-green-800 hover:bg-green-50 rounded transition-colors"
            title="Предварительный просмотр"
          >
            <FiEye size={16} />
          </button>
          <button
            onClick={() => handleEditTheory(row)}
            className="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded transition-colors"
            title="Редактировать"
          >
            <FiEdit2 size={16} />
          </button>
          <button
            onClick={() => handleDeleteTheory(row)}
            className="p-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded transition-colors"
            title="Удалить"
          >
            <FiTrash2 size={16} />
          </button>
        </div>
      )
    }
  ];

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-red-600 mb-2">Ошибка загрузки теорий</div>
          <button 
            onClick={() => refetch()} 
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Попробовать снова
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Теории</h1>
          <p className="text-gray-600 mt-1">
            Всего теорий: {theories.length}
          </p>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={handleCreateTheory}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <FiPlus size={16} />
            Создать теорию
          </button>
        </div>
      </div>

      {/* Search */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="flex items-center gap-4">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Поиск по названию или тегам..."
                onChange={handleSearch}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Theories Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <FiLoader className="animate-spin h-8 w-8 text-blue-600" />
            <span className="ml-2 text-gray-600">Загрузка теорий...</span>
          </div>
        ) : (
          <Table 
            columns={columns} 
            data={theories}
          />
        )}
      </div>

      {/* Theory Modal */}
      <TheoryModal
        theory={selectedTheory}
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setSelectedTheory(null);
        }}
        onSave={handleSaveTheory}
        isLoading={createTheoryMutation.isLoading || updateTheoryMutation.isLoading}
        sentences={sentences}
      />

      {/* Preview Modal */}
      {isPreviewOpen && previewTheory && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">
                {previewTheory.name}
              </h2>
              <button
                onClick={() => setIsPreviewOpen(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <FiX size={24} />
              </button>
            </div>
            <div className="p-6">
              {/* Tags */}
              {previewTheory.tags && previewTheory.tags.length > 0 && (
                <div className="mb-4">
                  <div className="flex flex-wrap gap-2">
                    {previewTheory.tags.map((tag, index) => (
                      <span 
                        key={index}
                        className="inline-flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
                      >
                        <FiTag size={12} />
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}
              
              {/* Content */}
              <div 
                className="prose prose-lg max-w-none"
                dangerouslySetInnerHTML={{ __html: previewTheory.content }}
              />

              {/* Related sentences */}
              {previewTheory.sentence_ids && previewTheory.sentence_ids.length > 0 && (
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Примеры предложений</h3>
                  <div className="space-y-2">
                    {getRelatedSentences(previewTheory.sentence_ids).map(sentence => (
                      <div key={sentence.id} className="p-3 bg-gray-50 rounded-md">
                        <div className="font-medium text-gray-900">{sentence.kaz_plaintext}</div>
                        <div className="text-gray-600">{sentence.rus_plaintext}</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
            <div className="flex justify-end p-6 border-t border-gray-200">
              <button
                onClick={() => setIsPreviewOpen(false)}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Закрыть
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Theories;
