import { FiUsers, FiBook, FiHelpCircle, FiFileText, FiEye, FiArrowUp, FiArrowRight, FiPlus, FiLoader } from 'react-icons/fi';
import { Link } from 'react-router-dom';
import Card from '../components/Card';
import StatCard from '../components/StatCard';
import Table from '../components/Table';
import Chart from '../components/Chart';
import { useDashboardStats, useRecentUsers } from '../hooks/useDashboard';
import { formatNumber, formatDate } from '../utils/helpers';

const Dashboard = () => {
  // Fetch dashboard data
  const { data: statsData, isLoading: statsLoading, error: statsError } = useDashboardStats();
  const { data: recentUsers, isLoading: usersLoading } = useRecentUsers();

  // Generate stats from API data
  const stats = statsData ? [
    {
      title: 'Всего пользователей',
      value: formatNumber(statsData.total_users),
      trendIcon: <FiArrowUp size={14} />,
      trend: 'up',
      trendValue: '+12.5%'
    },
    {
      title: 'Всего модулей',
      value: formatNumber(statsData.total_modules),
      trendIcon: <FiArrowUp size={14} />,
      trend: 'up',
      trendValue: '+2'
    },
    {
      title: 'Слов в словаре',
      value: formatNumber(statsData.total_words),
      trendIcon: <FiArrowUp size={14} />,
      trend: 'up',
      trendValue: '+156'
    },
    {
      title: 'Средний прогресс',
      value: `${statsData.avg_completion_rate}%`,
      trendIcon: <FiArrowUp size={14} />,
      trend: 'up',
      trendValue: '+3.2%'
    },
  ] : [];

  // Sample data for user registrations chart
  const usersChartData = {
    labels: ['Янв', 'Фев', 'Мар', 'Апр', 'Май', 'Июн', 'Июл'],
    datasets: [
      {
        label: 'Регистрации 2024',
        data: [45, 78, 123, 156, 189, 234, 267],
        borderColor: 'rgb(37, 99, 235)', // Blue
        backgroundColor: 'rgba(37, 99, 235, 0.1)',
        tension: 0.4,
        borderWidth: 2,
      },
      {
        label: 'Регистрации 2023',
        data: [23, 45, 67, 89, 112, 134, 156],
        borderColor: 'rgb(209, 213, 219)', // Gray
        backgroundColor: 'rgba(209, 213, 219, 0.1)',
        tension: 0.4,
        borderWidth: 2,
        borderDash: [5, 5],
      },
    ],
  };

  // Sample data for popular modules
  const modulesChartData = {
    labels: ['Приветствие', 'Семья', 'Еда', 'Время', 'Цвета', 'Числа'],
    datasets: [
      {
        data: [234, 189, 156, 134, 112, 89],
        backgroundColor: [
          'rgba(37, 99, 235, 0.7)', // Blue
          'rgba(16, 185, 129, 0.7)', // Green
          'rgba(245, 158, 11, 0.7)', // Yellow
          'rgba(239, 68, 68, 0.7)',  // Red
          'rgba(139, 92, 246, 0.7)', // Purple
          'rgba(107, 114, 128, 0.7)', // Gray
        ],
        borderWidth: 0,
      },
    ],
  };

  // Sample data for user progress distribution
  const progressChartData = {
    labels: ['Начинающие (0-25%)', 'Базовый (26-50%)', 'Средний (51-75%)', 'Продвинутый (76-100%)'],
    datasets: [
      {
        data: [28.5, 34.2, 25.8, 11.5],
        backgroundColor: [
          'rgba(239, 68, 68, 0.7)',  // Red
          'rgba(245, 158, 11, 0.7)', // Yellow
          'rgba(16, 185, 129, 0.7)', // Green
          'rgba(37, 99, 235, 0.7)',  // Blue
        ],
        borderWidth: 0,
      },
    ],
  };

  const lineChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true,
        position: 'top',
        align: 'end',
        labels: {
          boxWidth: 8,
          usePointStyle: true,
          pointStyle: 'circle',
        },
      },
      tooltip: {
        mode: 'index',
        intersect: false,
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
      },
      y: {
        grid: {
          borderDash: [2, 2],
        },
        ticks: {
          callback: function(value) {
            return value / 1000 + 'k';
          },
        },
      },
    },
    elements: {
      point: {
        radius: 0,
        hoverRadius: 6,
      },
    },
  };

  const barChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        mode: 'index',
        intersect: false,
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
      },
      y: {
        grid: {
          borderDash: [2, 2],
        },
        ticks: {
          callback: function(value) {
            return value / 1000 + 'k';
          },
        },
      },
    },
  };

  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right',
        align: 'center',
        labels: {
          boxWidth: 8,
          usePointStyle: true,
          pointStyle: 'circle',
        },
      },
    },
    cutout: '65%',
  };

  // Sample data for recent users table
  const columns = [
    { header: 'Пользователь', accessor: 'user' },
    { header: 'Email', accessor: 'email' },
    { header: 'Дата регистрации', accessor: 'date' },
    { header: 'Прогресс', accessor: 'progress' },
    {
      header: 'Статус',
      accessor: 'status',
      render: (row) => {
        const statusColors = {
          active: 'bg-green-100 text-green-800',
          learning: 'bg-blue-100 text-blue-800',
          inactive: 'bg-gray-100 text-gray-800',
        };

        return (
          <span className={`px-2 py-1 rounded-full text-xs ${statusColors[row.status.toLowerCase()]}`}>
            {row.status === 'active' ? 'Активен' : row.status === 'learning' ? 'Изучает' : 'Неактивен'}
          </span>
        );
      }
    },
  ];

  // Transform recent users data for table
  const tableData = recentUsers ? recentUsers.map(user => ({
    user: `${user.name} ${user.surname}`,
    email: user.email,
    date: formatDate(user.created_at),
    progress: `${user.progress}%`,
    status: user.status === 'active' ? 'active' : user.progress > 50 ? 'learning' : 'inactive'
  })) : [];

  const usersTabs = [
    { label: 'Регистрации', active: true },
    { label: 'Активность', active: false },
    { label: 'Прогресс', active: false },
  ];

  const quickActions = [
    { label: 'Добавить слово', icon: <FiPlus size={16} />, path: '/content/words/new' },
    { label: 'Создать модуль', icon: <FiBook size={16} />, path: '/content/modules/new' },
    { label: 'Новый вопрос', icon: <FiHelpCircle size={16} />, path: '/content/questions/new' },
    { label: 'Написать теорию', icon: <FiFileText size={16} />, path: '/content/theories/new' },
  ];

  // Loading state
  if (statsLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <FiLoader className="animate-spin h-8 w-8 text-blue-600" />
        <span className="ml-2 text-gray-600">Загрузка данных...</span>
      </div>
    );
  }

  // Error state
  if (statsError) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-red-600 mb-2">Ошибка загрузки данных</div>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Обновить
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Dashboard</h1>
        <div className="flex space-x-2">
          {quickActions.map((action, index) => (
            <Link
              key={index}
              to={action.path}
              className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm"
            >
              {action.icon}
              <span>{action.label}</span>
            </Link>
          ))}
        </div>
      </div>

      {/* Stats Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {stats.map((stat, index) => (
          <StatCard
            key={index}
            title={stat.title}
            value={stat.value}
            trendIcon={stat.trendIcon}
            trend={stat.trend}
            trendValue={stat.trendValue}
          />
        ))}
      </div>

      {/* Users Chart */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-6">
        <div className="lg:col-span-2">
          <Card
            title="Регистрации пользователей"
            tabs={usersTabs}
            className="h-full"
          >
            <div className="h-64">
              <Chart
                type="line"
                data={usersChartData}
                options={lineChartOptions}
              />
            </div>
          </Card>
        </div>

        <div>
          <Card
            title="Последние пользователи"
            className="h-full"
          >
            {usersLoading ? (
              <div className="flex items-center justify-center h-32">
                <FiLoader className="animate-spin h-6 w-6 text-blue-600" />
              </div>
            ) : (
              <div className="space-y-3">
                {tableData.slice(0, 5).map((user, index) => (
                  <div key={index} className="flex items-center justify-between p-2 hover:bg-gray-50 rounded">
                    <div>
                      <div className="font-medium text-sm">{user.user}</div>
                      <div className="text-xs text-gray-500">{user.email}</div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">{user.progress}</div>
                      <div className="text-xs text-gray-500">{user.date}</div>
                    </div>
                  </div>
                ))}
                <Link
                  to="/users"
                  className="block text-center text-sm text-blue-600 hover:text-blue-800 mt-3"
                >
                  Посмотреть всех →
                </Link>
              </div>
            )}
          </Card>
        </div>
      </div>

      {/* Analytics Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-6">
        <Card title="Популярные модули">
          <div className="h-64">
            <Chart
              type="bar"
              data={modulesChartData}
              options={barChartOptions}
            />
          </div>
        </Card>

        <Card title="Распределение прогресса">
          <div className="h-64 flex items-center justify-center">
            <div className="w-48 h-48">
              <Chart
                type="doughnut"
                data={progressChartData}
                options={pieChartOptions}
              />
            </div>
            <div className="ml-4 space-y-2">
              {progressChartData.labels.map((label, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <span className="inline-block w-2 h-2 rounded-full mr-2" style={{ backgroundColor: progressChartData.datasets[0].backgroundColor[index] }}></span>
                    <span className="text-sm text-gray-700">{label}</span>
                  </div>
                  <span className="text-xs text-gray-500 ml-4">{progressChartData.datasets[0].data[index]}%</span>
                </div>
              ))}
            </div>
          </div>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card title="Недавние пользователи" className="mb-6">
        <Table columns={columns} data={tableData} />
      </Card>
    </div>
  );
};

export default Dashboard;
