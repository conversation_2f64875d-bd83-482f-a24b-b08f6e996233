import apiClient from './api';

export const filesService = {
  // Upload single audio file
  async uploadAudio(audioFile) {
    const formData = new FormData();
    formData.append('audio', audioFile);

    const response = await apiClient.post('/files/upload/audio', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  },

  // Upload single image file
  async uploadImage(imageFile) {
    const formData = new FormData();
    formData.append('image', imageFile);

    const response = await apiClient.post('/files/upload/image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  },

  // Upload multiple files
  async uploadMultiple(files) {
    const formData = new FormData();
    files.forEach(file => {
      formData.append('files', file);
    });

    const response = await apiClient.post('/files/upload/multiple', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  },

  // Get files list
  async getFilesList(bucket, prefix = '') {
    const params = new URLSearchParams({ bucket });
    if (prefix) params.append('prefix', prefix);

    const response = await apiClient.get(`/files/list?${params}`);
    return response.data;
  },

  // Delete file
  async deleteFile(fileUrl) {
    const response = await apiClient.delete(`/files/delete?url=${encodeURIComponent(fileUrl)}`);
    return response.data;
  },

  // Get audio files
  async getAudioFiles(prefix = '') {
    return this.getFilesList('klingo-audio', prefix);
  },

  // Get image files
  async getImageFiles(prefix = '') {
    return this.getFilesList('klingo-images', prefix);
  }
};

export default filesService;
