import apiClient from './api';

export const filesService = {
  // Upload single audio file
  async uploadAudio(audioFile) {
    const formData = new FormData();
    formData.append('audio', audioFile);

    const response = await apiClient.post('/files/upload/audio', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  },

  // Upload single image file
  async uploadImage(imageFile) {
    const formData = new FormData();
    formData.append('image', imageFile);

    const response = await apiClient.post('/files/upload/image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  },

  // Upload multiple files
  async uploadMultiple(files) {
    const formData = new FormData();
    files.forEach(file => {
      formData.append('files', file);
    });

    const response = await apiClient.post('/files/upload/multiple', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  },

  // Get files list
  async getFilesList(bucket, prefix = '') {
    const params = new URLSearchParams({ bucket });
    if (prefix) params.append('prefix', prefix);

    const response = await apiClient.get(`/files/list?${params}`);
    return response.data;
  },

  // Delete file
  async deleteFile(fileUrl) {
    const response = await apiClient.delete(`/files/delete?url=${encodeURIComponent(fileUrl)}`);
    return response.data;
  },

  // Get audio files
  async getAudioFiles(prefix = '') {
    return this.getFilesList('klingo-audio', prefix);
  },

  // Get image files
  async getImageFiles(prefix = '') {
    return this.getFilesList('klingo-images', prefix);
  },

  // Get all files with optional filters
  async getFiles(params = {}) {
    const response = await apiClient.get('/files', { params });
    return response.data;
  },

  // Get storage statistics
  async getStorageStats() {
    const response = await apiClient.get('/files/stats');
    return response.data;
  },

  // Get buckets list
  async getBuckets() {
    const response = await apiClient.get('/files/buckets');
    return response.data;
  },

  // Upload any file type
  async uploadFile(file, bucket = 'default') {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('bucket', bucket);

    const response = await apiClient.post('/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  },

  // File validation
  validateFile(file, options = {}) {
    const errors = [];
    const {
      maxSize = 10 * 1024 * 1024, // 10MB default
      allowedTypes = [],
      allowedExtensions = []
    } = options;

    // Check file size
    if (file.size > maxSize) {
      errors.push(`File size exceeds ${maxSize / (1024 * 1024)}MB limit`);
    }

    // Check file type
    if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
      errors.push(`File type ${file.type} is not allowed`);
    }

    // Check file extension
    if (allowedExtensions.length > 0) {
      const extension = file.name.split('.').pop().toLowerCase();
      if (!allowedExtensions.includes(extension)) {
        errors.push(`File extension .${extension} is not allowed`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
};

export default filesService;
