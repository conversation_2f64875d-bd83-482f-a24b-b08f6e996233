import React from 'react';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { FiUsers, FiUserPlus, FiUserCheck, FiTrendingUp } from 'react-icons/fi';
import { formatNumber, formatPercentage } from '../../utils/helpers';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const StatCard = ({ title, value, change, icon: Icon, color = 'blue' }) => {
  const isPositive = change > 0;
  
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{formatNumber(value)}</p>
          {change !== undefined && (
            <p className={`text-sm ${isPositive ? 'text-green-600' : 'text-red-600'} flex items-center gap-1`}>
              <FiTrendingUp size={14} className={isPositive ? '' : 'rotate-180'} />
              {isPositive ? '+' : ''}{change}% за месяц
            </p>
          )}
        </div>
        <div className={`p-3 rounded-full bg-${color}-100`}>
          <Icon className={`text-${color}-600`} size={24} />
        </div>
      </div>
    </div>
  );
};

const UserAnalytics = ({ data }) => {
  const {
    totalUsers = 0,
    newUsers = 0,
    activeUsers = 0,
    userGrowth = 0,
    newUserGrowth = 0,
    activeUserGrowth = 0,
    userRegistrations = [],
    userActivity = [],
    usersByRole = [],
    usersByStatus = []
  } = data;

  // User registrations chart
  const registrationChartData = {
    labels: userRegistrations.map(item => item.date),
    datasets: [
      {
        label: 'Новые регистрации',
        data: userRegistrations.map(item => item.count),
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
      },
    ],
  };

  // User activity chart
  const activityChartData = {
    labels: userActivity.map(item => item.date),
    datasets: [
      {
        label: 'Активные пользователи',
        data: userActivity.map(item => item.active),
        backgroundColor: 'rgba(34, 197, 94, 0.8)',
      },
      {
        label: 'Новые пользователи',
        data: userActivity.map(item => item.new),
        backgroundColor: 'rgba(59, 130, 246, 0.8)',
      },
    ],
  };

  // Users by role chart
  const roleChartData = {
    labels: usersByRole.map(item => item.role),
    datasets: [
      {
        data: usersByRole.map(item => item.count),
        backgroundColor: [
          'rgba(59, 130, 246, 0.8)',
          'rgba(34, 197, 94, 0.8)',
          'rgba(251, 191, 36, 0.8)',
          'rgba(239, 68, 68, 0.8)',
        ],
      },
    ],
  };

  // Users by status chart
  const statusChartData = {
    labels: usersByStatus.map(item => item.status),
    datasets: [
      {
        data: usersByStatus.map(item => item.count),
        backgroundColor: [
          'rgba(34, 197, 94, 0.8)',
          'rgba(251, 191, 36, 0.8)',
          'rgba(239, 68, 68, 0.8)',
        ],
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  const doughnutOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'bottom',
      },
    },
  };

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <StatCard
          title="Всего пользователей"
          value={totalUsers}
          change={userGrowth}
          icon={FiUsers}
          color="blue"
        />
        <StatCard
          title="Новые пользователи"
          value={newUsers}
          change={newUserGrowth}
          icon={FiUserPlus}
          color="green"
        />
        <StatCard
          title="Активные пользователи"
          value={activeUsers}
          change={activeUserGrowth}
          icon={FiUserCheck}
          color="purple"
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User Registrations */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Регистрации пользователей
          </h3>
          <div className="h-64">
            <Line data={registrationChartData} options={chartOptions} />
          </div>
        </div>

        {/* User Activity */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Активность пользователей
          </h3>
          <div className="h-64">
            <Bar data={activityChartData} options={chartOptions} />
          </div>
        </div>

        {/* Users by Role */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Пользователи по ролям
          </h3>
          <div className="h-64 flex items-center justify-center">
            <div className="w-48 h-48">
              <Doughnut data={roleChartData} options={doughnutOptions} />
            </div>
          </div>
        </div>

        {/* Users by Status */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Пользователи по статусу
          </h3>
          <div className="h-64 flex items-center justify-center">
            <div className="w-48 h-48">
              <Doughnut data={statusChartData} options={doughnutOptions} />
            </div>
          </div>
        </div>
      </div>

      {/* Detailed Stats Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Детальная статистика
          </h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{formatNumber(totalUsers)}</div>
              <div className="text-sm text-gray-600">Всего пользователей</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{formatNumber(activeUsers)}</div>
              <div className="text-sm text-gray-600">Активных за месяц</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{formatNumber(newUsers)}</div>
              <div className="text-sm text-gray-600">Новых за месяц</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {formatPercentage(activeUsers, totalUsers)}
              </div>
              <div className="text-sm text-gray-600">Коэффициент активности</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserAnalytics;
