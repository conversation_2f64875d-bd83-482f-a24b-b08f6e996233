import React, { useState } from 'react';
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  FiRefreshCw,
  FiFolder,
  FiHardDrive,
  FiLoader
} from 'react-icons/fi';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import FileGrid from '../components/FileGrid';
import FileUpload from '../components/FileUpload';
import FilePreview from '../components/FilePreview';
import { mockDataService } from '../services/mockData';
import fileService from '../services/files';
import { debounce, formatFileSize } from '../utils/helpers';

// Use mock service in development mode
const USE_MOCK = import.meta.env.DEV;

// Mock files data
const mockFiles = [
  {
    id: 1,
    name: 'hello-world.mp3',
    size: 1024 * 512, // 512KB
    type: 'audio/mpeg',
    url: 'http://minio:9000/klingo-audio/hello-world.mp3',
    lastModified: '2024-01-01T12:00:00Z',
    bucket: 'klingo-audio'
  },
  {
    id: 2,
    name: 'lesson-1-cover.jpg',
    size: 1024 * 256, // 256KB
    type: 'image/jpeg',
    url: 'http://minio:9000/klingo-images/lesson-1-cover.jpg',
    lastModified: '2024-01-02T12:00:00Z',
    bucket: 'klingo-images'
  },
  {
    id: 3,
    name: 'grammar-rules.pdf',
    size: 1024 * 1024 * 2, // 2MB
    type: 'application/pdf',
    url: 'http://minio:9000/klingo-documents/grammar-rules.pdf',
    lastModified: '2024-01-03T12:00:00Z',
    bucket: 'klingo-documents'
  },
  {
    id: 4,
    name: 'pronunciation-guide.mp3',
    size: 1024 * 1024, // 1MB
    type: 'audio/mpeg',
    url: 'http://minio:9000/klingo-audio/pronunciation-guide.mp3',
    lastModified: '2024-01-04T12:00:00Z',
    bucket: 'klingo-audio'
  },
  {
    id: 5,
    name: 'vocabulary-cards.png',
    size: 1024 * 128, // 128KB
    type: 'image/png',
    url: 'http://minio:9000/klingo-images/vocabulary-cards.png',
    lastModified: '2024-01-05T12:00:00Z',
    bucket: 'klingo-images'
  }
];

const FileManager = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBucket, setSelectedBucket] = useState('');
  const [fileTypeFilter, setFileTypeFilter] = useState('');
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [showUpload, setShowUpload] = useState(false);
  const [previewFile, setPreviewFile] = useState(null);

  const queryClient = useQueryClient();

  // Fetch files
  const { data: files = [], isLoading, error, refetch } = useQuery({
    queryKey: ['files', searchTerm, selectedBucket, fileTypeFilter],
    queryFn: async () => {
      if (USE_MOCK) {
        let filteredFiles = [...mockFiles];
        
        if (searchTerm) {
          filteredFiles = filteredFiles.filter(file => 
            file.name.toLowerCase().includes(searchTerm.toLowerCase())
          );
        }
        
        if (selectedBucket) {
          filteredFiles = filteredFiles.filter(file => file.bucket === selectedBucket);
        }
        
        if (fileTypeFilter) {
          filteredFiles = filteredFiles.filter(file => {
            if (fileTypeFilter === 'images') return file.type.startsWith('image/');
            if (fileTypeFilter === 'audio') return file.type.startsWith('audio/');
            if (fileTypeFilter === 'documents') return file.type.includes('pdf') || file.type.includes('document');
            return true;
          });
        }
        
        return filteredFiles;
      } else {
        return await fileService.getFiles({ search: searchTerm, bucket: selectedBucket, type: fileTypeFilter });
      }
    },
    staleTime: 30 * 1000,
  });

  // Get storage stats
  const { data: storageStats } = useQuery({
    queryKey: ['storage-stats'],
    queryFn: async () => {
      if (USE_MOCK) {
        return {
          totalSize: 1024 * 1024 * 1024 * 5, // 5GB
          usedSize: 1024 * 1024 * 500, // 500MB
          fileCount: mockFiles.length,
          buckets: [
            { name: 'klingo-audio', fileCount: 2, size: 1024 * 1024 * 1.5 },
            { name: 'klingo-images', fileCount: 2, size: 1024 * 384 },
            { name: 'klingo-documents', fileCount: 1, size: 1024 * 1024 * 2 }
          ]
        };
      } else {
        return await fileService.getStorageStats();
      }
    },
    staleTime: 60 * 1000,
  });

  // Upload file mutation
  const uploadFileMutation = useMutation({
    mutationFn: async (file) => {
      if (USE_MOCK) {
        await new Promise(resolve => setTimeout(resolve, 2000));
        return {
          id: Date.now(),
          name: file.name,
          size: file.size,
          type: file.type,
          url: `http://minio:9000/klingo-uploads/${file.name}`,
          lastModified: new Date().toISOString(),
          bucket: 'klingo-uploads'
        };
      } else {
        return await fileService.uploadFile(file);
      }
    },
    onSuccess: () => {
      toast.success('Файл успешно загружен');
      queryClient.invalidateQueries(['files']);
      queryClient.invalidateQueries(['storage-stats']);
    },
    onError: (error) => {
      toast.error('Ошибка при загрузке файла');
      console.error('Upload error:', error);
    }
  });

  // Delete file mutation
  const deleteFileMutation = useMutation({
    mutationFn: async (file) => {
      if (USE_MOCK) {
        await new Promise(resolve => setTimeout(resolve, 500));
        return { success: true };
      } else {
        return await fileService.deleteFile(file.id);
      }
    },
    onSuccess: () => {
      toast.success('Файл успешно удален');
      queryClient.invalidateQueries(['files']);
      queryClient.invalidateQueries(['storage-stats']);
    },
    onError: (error) => {
      toast.error('Ошибка при удалении файла');
      console.error('Delete error:', error);
    }
  });

  // Debounced search
  const debouncedSearch = debounce((value) => {
    setSearchTerm(value);
  }, 300);

  const handleSearch = (e) => {
    debouncedSearch(e.target.value);
  };

  const handleUpload = async (file) => {
    await uploadFileMutation.mutateAsync(file);
  };

  const handlePreview = (file) => {
    setPreviewFile(file);
  };

  const handleDownload = (file) => {
    const link = document.createElement('a');
    link.href = file.url;
    link.download = file.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    toast.success('Загрузка файла начата');
  };

  const handleDelete = (file) => {
    deleteFileMutation.mutate(file);
  };

  const handleCopyUrl = (file) => {
    navigator.clipboard.writeText(file.url);
    toast.success('URL скопирован в буфер обмена');
  };

  const getUsagePercentage = () => {
    if (!storageStats) return 0;
    return (storageStats.usedSize / storageStats.totalSize) * 100;
  };

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-red-600 mb-2">Ошибка загрузки файлов</div>
          <button 
            onClick={() => refetch()} 
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Попробовать снова
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Файловый менеджер</h1>
          <p className="text-gray-600 mt-1">
            Всего файлов: {files.length}
            {storageStats && (
              <span className="ml-4">
                Использовано: {formatFileSize(storageStats.usedSize)} из {formatFileSize(storageStats.totalSize)}
              </span>
            )}
          </p>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={() => refetch()}
            className="flex items-center gap-2 px-3 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            <FiRefreshCw size={16} />
            Обновить
          </button>
          <button
            onClick={() => setShowUpload(!showUpload)}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <FiUpload size={16} />
            Загрузить файлы
          </button>
        </div>
      </div>

      {/* Storage stats */}
      {storageStats && (
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-medium text-gray-900">Использование хранилища</h3>
            <span className="text-sm text-gray-500">
              {getUsagePercentage().toFixed(1)}% использовано
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${getUsagePercentage()}%` }}
            ></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {storageStats.buckets?.map(bucket => (
              <div key={bucket.name} className="flex items-center gap-3 p-3 bg-gray-50 rounded-md">
                <FiFolder className="text-blue-600" size={20} />
                <div>
                  <div className="font-medium text-sm">{bucket.name}</div>
                  <div className="text-xs text-gray-500">
                    {bucket.fileCount} файлов • {formatFileSize(bucket.size)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Upload area */}
      {showUpload && (
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-medium text-gray-900">Загрузка файлов</h3>
            <button
              onClick={() => setShowUpload(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          </div>
          <FileUpload onUpload={handleUpload} />
        </div>
      )}

      {/* Search and filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="flex items-center gap-4">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Поиск файлов..."
                onChange={handleSearch}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
          
          <select
            value={selectedBucket}
            onChange={(e) => setSelectedBucket(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Все папки</option>
            {storageStats?.buckets?.map(bucket => (
              <option key={bucket.name} value={bucket.name}>
                {bucket.name}
              </option>
            ))}
          </select>
          
          <select
            value={fileTypeFilter}
            onChange={(e) => setFileTypeFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Все типы</option>
            <option value="images">Изображения</option>
            <option value="audio">Аудио</option>
            <option value="documents">Документы</option>
          </select>
          
          <div className="flex items-center border border-gray-300 rounded-md">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 ${viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-600'}`}
            >
              <FiGrid size={16} />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 ${viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-600'}`}
            >
              <FiList size={16} />
            </button>
          </div>
        </div>
      </div>

      {/* Files */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <FiLoader className="animate-spin h-8 w-8 text-blue-600" />
            <span className="ml-2 text-gray-600">Загрузка файлов...</span>
          </div>
        ) : (
          <FileGrid
            files={files}
            loading={isLoading}
            onPreview={handlePreview}
            onDownload={handleDownload}
            onDelete={handleDelete}
            onCopyUrl={handleCopyUrl}
          />
        )}
      </div>

      {/* File Preview Modal */}
      <FilePreview
        file={previewFile}
        isOpen={!!previewFile}
        onClose={() => setPreviewFile(null)}
        onDownload={handleDownload}
        onDelete={handleDelete}
        onCopyUrl={handleCopyUrl}
      />
    </div>
  );
};

export default FileManager;
