import React, { useState } from 'react';
import { FiPlus, FiEdit2, FiTrash2, FiPlay, FiPause, <PERSON>S<PERSON>ch, <PERSON>L<PERSON>der, FiTag } from 'react-icons/fi';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import SentenceModal from '../components/SentenceModal';
import Table from '../components/Table';
import { mockDataService } from '../services/mockData';
import contentService from '../services/content';
import { debounce, truncateText } from '../utils/helpers';

// Use mock service in development mode
const USE_MOCK = import.meta.env.DEV;

// Mock theories data
const mockTheories = [
  { id: 1, name: 'Базовые приветствия' },
  { id: 2, name: 'Семья и родственники' },
  { id: 3, name: 'Еда и напитки' },
  { id: 4, name: 'Время и даты' },
];

// Mock sentences data
const mockSentences = [
  {
    id: 1,
    kaz_plaintext: 'Менің атым Айдар.',
    rus_plaintext: 'Меня зовут Айдар.',
    theory_ids: [1],
    audio_url: 'http://minio:9000/klingo-audio/mening-atym-aidar.mp3'
  },
  {
    id: 2,
    kaz_plaintext: 'Сіз қалай жақсысыз?',
    rus_plaintext: 'Как дела?',
    theory_ids: [1],
    audio_url: 'http://minio:9000/klingo-audio/siz-qalay-jaqsysyz.mp3'
  },
  {
    id: 3,
    kaz_plaintext: 'Менің анам үйде.',
    rus_plaintext: 'Моя мама дома.',
    theory_ids: [2],
    audio_url: 'http://minio:9000/klingo-audio/mening-anam-uyde.mp3'
  },
];

const Sentences = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSentence, setSelectedSentence] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [playingAudio, setPlayingAudio] = useState(null);
  const [audioElements, setAudioElements] = useState({});

  const queryClient = useQueryClient();

  // Fetch sentences
  const { data: sentences = [], isLoading, error, refetch } = useQuery({
    queryKey: ['sentences', searchTerm],
    queryFn: async () => {
      if (USE_MOCK) {
        let filteredSentences = [...mockSentences];
        if (searchTerm) {
          filteredSentences = filteredSentences.filter(sentence => 
            sentence.kaz_plaintext.toLowerCase().includes(searchTerm.toLowerCase()) ||
            sentence.rus_plaintext.toLowerCase().includes(searchTerm.toLowerCase())
          );
        }
        return filteredSentences;
      } else {
        return await contentService.sentences.getAll();
      }
    },
    staleTime: 30 * 1000,
  });

  // Fetch theories for modal
  const { data: theories = [] } = useQuery({
    queryKey: ['theories'],
    queryFn: async () => {
      if (USE_MOCK) {
        return mockTheories;
      } else {
        return await contentService.theories.getAll();
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Create sentence mutation
  const createSentenceMutation = useMutation({
    mutationFn: async (sentenceData) => {
      if (USE_MOCK) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        return { id: Date.now(), ...sentenceData };
      } else {
        return await contentService.sentences.create(sentenceData);
      }
    },
    onSuccess: () => {
      toast.success('Предложение успешно добавлено');
      queryClient.invalidateQueries(['sentences']);
      setIsModalOpen(false);
      setSelectedSentence(null);
    },
    onError: (error) => {
      toast.error('Ошибка при добавлении предложения');
      console.error('Create sentence error:', error);
    }
  });

  // Update sentence mutation
  const updateSentenceMutation = useMutation({
    mutationFn: async ({ id, sentenceData }) => {
      if (USE_MOCK) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        return { id, ...sentenceData };
      } else {
        return await contentService.sentences.update(id, sentenceData);
      }
    },
    onSuccess: () => {
      toast.success('Предложение успешно обновлено');
      queryClient.invalidateQueries(['sentences']);
      setIsModalOpen(false);
      setSelectedSentence(null);
    },
    onError: (error) => {
      toast.error('Ошибка при обновлении предложения');
      console.error('Update sentence error:', error);
    }
  });

  // Delete sentence mutation
  const deleteSentenceMutation = useMutation({
    mutationFn: async (sentenceId) => {
      if (USE_MOCK) {
        await new Promise(resolve => setTimeout(resolve, 500));
        return { success: true };
      } else {
        return await contentService.sentences.delete(sentenceId);
      }
    },
    onSuccess: () => {
      toast.success('Предложение успешно удалено');
      queryClient.invalidateQueries(['sentences']);
    },
    onError: (error) => {
      toast.error('Ошибка при удалении предложения');
      console.error('Delete sentence error:', error);
    }
  });

  // Debounced search
  const debouncedSearch = debounce((value) => {
    setSearchTerm(value);
  }, 300);

  const handleSearch = (e) => {
    debouncedSearch(e.target.value);
  };

  const handleCreateSentence = () => {
    setSelectedSentence(null);
    setIsModalOpen(true);
  };

  const handleEditSentence = (sentence) => {
    setSelectedSentence(sentence);
    setIsModalOpen(true);
  };

  const handleDeleteSentence = (sentence) => {
    if (window.confirm(`Вы уверены, что хотите удалить предложение "${truncateText(sentence.kaz_plaintext, 50)}"?`)) {
      deleteSentenceMutation.mutate(sentence.id);
    }
  };

  const handleSaveSentence = (sentenceData) => {
    if (selectedSentence) {
      updateSentenceMutation.mutate({ id: selectedSentence.id, sentenceData });
    } else {
      createSentenceMutation.mutate(sentenceData);
    }
  };

  const handlePlayAudio = (sentence) => {
    if (!sentence.audio_url) return;

    // Stop currently playing audio
    if (playingAudio && audioElements[playingAudio]) {
      audioElements[playingAudio].pause();
      if (playingAudio === sentence.id) {
        setPlayingAudio(null);
        return;
      }
    }

    // Create or get audio element
    let audio = audioElements[sentence.id];
    if (!audio) {
      audio = new Audio(sentence.audio_url);
      audio.addEventListener('ended', () => setPlayingAudio(null));
      setAudioElements(prev => ({ ...prev, [sentence.id]: audio }));
    }

    audio.play();
    setPlayingAudio(sentence.id);
  };

  const getTheoryNames = (theoryIds) => {
    if (!theoryIds || theoryIds.length === 0) return 'Нет теорий';
    
    const names = theoryIds
      .map(id => theories.find(theory => theory.id === id)?.name)
      .filter(Boolean);
    
    return names.length > 0 ? names.join(', ') : 'Неизвестные теории';
  };

  // Table columns
  const columns = [
    {
      header: 'Казахский',
      accessor: 'kaz_plaintext',
      render: (row) => (
        <div className="max-w-xs">
          <div className="font-medium text-gray-900">
            {truncateText(row.kaz_plaintext, 60)}
          </div>
        </div>
      )
    },
    {
      header: 'Русский',
      accessor: 'rus_plaintext',
      render: (row) => (
        <div className="max-w-xs">
          <div className="text-gray-700">
            {truncateText(row.rus_plaintext, 60)}
          </div>
        </div>
      )
    },
    {
      header: 'Теории',
      accessor: 'theory_ids',
      render: (row) => (
        <div className="flex items-center gap-1">
          <FiTag size={14} className="text-gray-400" />
          <span className="text-sm text-gray-600">
            {truncateText(getTheoryNames(row.theory_ids), 40)}
          </span>
        </div>
      )
    },
    {
      header: 'Аудио',
      accessor: 'audio_url',
      render: (row) => (
        <div>
          {row.audio_url ? (
            <button
              onClick={() => handlePlayAudio(row)}
              className="flex items-center gap-2 px-2 py-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded transition-colors"
            >
              {playingAudio === row.id ? (
                <FiPause size={16} />
              ) : (
                <FiPlay size={16} />
              )}
              <span className="text-sm">Прослушать</span>
            </button>
          ) : (
            <span className="text-gray-400 text-sm">Нет аудио</span>
          )}
        </div>
      )
    },
    {
      header: 'Действия',
      accessor: 'actions',
      render: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleEditSentence(row)}
            className="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded transition-colors"
            title="Редактировать"
          >
            <FiEdit2 size={16} />
          </button>
          <button
            onClick={() => handleDeleteSentence(row)}
            className="p-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded transition-colors"
            title="Удалить"
          >
            <FiTrash2 size={16} />
          </button>
        </div>
      )
    }
  ];

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-red-600 mb-2">Ошибка загрузки предложений</div>
          <button 
            onClick={() => refetch()} 
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Попробовать снова
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Предложения</h1>
          <p className="text-gray-600 mt-1">
            Всего предложений: {sentences.length}
          </p>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={handleCreateSentence}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <FiPlus size={16} />
            Добавить предложение
          </button>
        </div>
      </div>

      {/* Search */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="flex items-center gap-4">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Поиск по казахскому или русскому предложению..."
                onChange={handleSearch}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Sentences Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <FiLoader className="animate-spin h-8 w-8 text-blue-600" />
            <span className="ml-2 text-gray-600">Загрузка предложений...</span>
          </div>
        ) : (
          <Table 
            columns={columns} 
            data={sentences}
          />
        )}
      </div>

      {/* Sentence Modal */}
      <SentenceModal
        sentence={selectedSentence}
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setSelectedSentence(null);
        }}
        onSave={handleSaveSentence}
        isLoading={createSentenceMutation.isLoading || updateSentenceMutation.isLoading}
        theories={theories}
      />
    </div>
  );
};

export default Sentences;
