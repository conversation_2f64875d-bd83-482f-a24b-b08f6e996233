import { useLocation, Link } from 'react-router-dom';
import {
  FiHome,
  FiUsers,
  FiShoppingBag,
  FiSettings,
  FiBarChart2,
  FiMail,
  FiCalendar,
  FiMenu,
  FiX,
  FiFileText,
  FiUser,
  FiCreditCard,
  FiGrid
} from 'react-icons/fi';

const Sidebar = ({ collapsed, toggleSidebar }) => {
  const location = useLocation();

  const categories = [
    {
      title: 'Favorites',
      items: [
        { name: 'Overview', icon: <FiHome size={18} />, path: '/' },
        { name: 'Projects', icon: <FiGrid size={18} />, path: '/projects' },
      ]
    },
    {
      title: 'Dashboards',
      items: [
        { name: 'Overview', icon: <FiBarChart2 size={18} />, path: '/dashboard/overview' },
        { name: 'eCommerce', icon: <FiShoppingBag size={18} />, path: '/dashboard/ecommerce' },
        { name: 'Projects', icon: <FiGrid size={18} />, path: '/dashboard/projects' },
      ]
    },
    {
      title: 'Pages',
      items: [
        { name: 'User Profile', icon: <FiUser size={18} />, path: '/pages/profile' },
        { name: 'Overview', icon: <FiHome size={18} />, path: '/pages/overview' },
        { name: 'Projects', icon: <FiGrid size={18} />, path: '/pages/projects' },
        { name: 'Campaigns', icon: <FiMail size={18} />, path: '/pages/campaigns' },
        { name: 'Documents', icon: <FiFileText size={18} />, path: '/pages/documents' },
        { name: 'Followers', icon: <FiUsers size={18} />, path: '/pages/followers' },
      ]
    }
  ];

  return (
    <div className={`bg-white text-gray-700 h-screen ${collapsed ? 'w-20' : 'w-64'} transition-all duration-300 fixed left-0 top-0 z-10 border-r border-gray-200`}>
      <div className="flex justify-between items-center p-5">
        {!collapsed && (
          <div className="flex items-center">
            <span className="font-bold text-lg">ByeWind</span>
          </div>
        )}
        <button
          onClick={toggleSidebar}
          className="p-2 rounded-md hover:bg-gray-100 focus:outline-none text-gray-500"
        >
          {collapsed ? <FiMenu size={20} /> : <FiX size={20} />}
        </button>
      </div>

      <div className="overflow-y-auto h-[calc(100vh-80px)] px-4">
        {categories.map((category, categoryIndex) => (
          <div key={categoryIndex} className="mb-6">
            {!collapsed && (
              <h3 className="text-xs uppercase text-gray-500 font-medium mb-2 px-2">{category.title}</h3>
            )}
            <nav>
              <ul className="space-y-1">
                {category.items.map((item, itemIndex) => {
                  const isActive = location.pathname === item.path ||
                                  (item.path !== '/' && location.pathname.startsWith(item.path));

                  return (
                    <li key={itemIndex}>
                      <Link
                        to={item.path}
                        className={`flex items-center py-2 px-3 rounded-lg ${
                          isActive
                            ? 'bg-primary-50 text-primary-600'
                            : 'hover:bg-gray-100 text-gray-700'
                        } transition-colors`}
                      >
                        <span className={`text-${isActive ? 'primary-600' : 'gray-500'} ${collapsed ? '' : 'mr-3'}`}>
                          {item.icon}
                        </span>
                        {!collapsed && <span className={`${isActive ? 'font-medium' : ''}`}>{item.name}</span>}
                      </Link>
                    </li>
                  );
                })}
              </ul>
            </nav>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Sidebar;
