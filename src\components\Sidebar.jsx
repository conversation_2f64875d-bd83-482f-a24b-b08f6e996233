import { useLocation, Link } from 'react-router-dom';
import { useState } from 'react';
import {
  FiMenu,
  FiX,
  FiChevronDown,
  FiChevronRight,
  FiBarChart,
  FiUsers,
  FiBook,
  FiType,
  FiMessageSquare,
  FiHelpCircle,
  FiFileText,
  FiPackage,
  FiFolder,
  FiTrendingUp,
  FiAward,
  FiSettings
} from 'react-icons/fi';
import { MENU_ITEMS } from '../utils/constants';

// Icon mapping
const iconMap = {
  BarChart3: FiBarChart,
  Users: FiUsers,
  BookOpen: FiBook,
  Type: FiType,
  MessageSquare: FiMessageSquare,
  HelpCircle: FiHelpCircle,
  FileText: FiFileText,
  Package: FiPackage,
  Folder: FiFolder,
  TrendingUp: FiTrendingUp,
  Award: FiAward,
  Settings: FiSettings
};

const Sidebar = ({ collapsed, toggleSidebar }) => {
  const location = useLocation();
  const [expandedItems, setExpandedItems] = useState({});

  const toggleExpanded = (itemId) => {
    setExpandedItems(prev => ({
      ...prev,
      [itemId]: !prev[itemId]
    }));
  };

  const renderIcon = (iconName, size = 18) => {
    const IconComponent = iconMap[iconName];
    return IconComponent ? <IconComponent size={size} /> : null;
  };

  return (
    <div className={`bg-white text-gray-700 h-screen ${collapsed ? 'w-20' : 'w-64'} transition-all duration-300 fixed left-0 top-0 z-10 border-r border-gray-200`}>
      <div className="flex justify-between items-center p-5">
        {!collapsed && (
          <div className="flex items-center">
            <span className="font-bold text-lg text-blue-600">Kazakh-Lingo</span>
          </div>
        )}
        <button
          onClick={toggleSidebar}
          className="p-2 rounded-md hover:bg-gray-100 focus:outline-none text-gray-500"
        >
          {collapsed ? <FiMenu size={20} /> : <FiX size={20} />}
        </button>
      </div>

      <div className="overflow-y-auto h-[calc(100vh-80px)] px-4">
        <nav>
          <ul className="space-y-1">
            {MENU_ITEMS.map((item) => {
              const hasChildren = item.children && item.children.length > 0;
              const isExpanded = expandedItems[item.id];
              const isActive = item.path && (location.pathname === item.path ||
                              (item.path !== '/' && location.pathname.startsWith(item.path)));

              // Check if any child is active
              const hasActiveChild = hasChildren && item.children.some(child =>
                location.pathname === child.path ||
                (child.path !== '/' && location.pathname.startsWith(child.path))
              );

              return (
                <li key={item.id}>
                  {hasChildren ? (
                    <div>
                      <button
                        onClick={() => toggleExpanded(item.id)}
                        className={`flex items-center justify-between w-full py-2 px-3 rounded-lg ${
                          hasActiveChild
                            ? 'bg-blue-50 text-blue-600'
                            : 'hover:bg-gray-100 text-gray-700'
                        } transition-colors`}
                      >
                        <div className="flex items-center">
                          <span className={`${hasActiveChild ? 'text-blue-600' : 'text-gray-500'} ${collapsed ? '' : 'mr-3'}`}>
                            {renderIcon(item.icon)}
                          </span>
                          {!collapsed && <span className={`${hasActiveChild ? 'font-medium' : ''}`}>{item.label}</span>}
                        </div>
                        {!collapsed && (
                          <span className={`${hasActiveChild ? 'text-blue-600' : 'text-gray-500'}`}>
                            {isExpanded ? <FiChevronDown size={16} /> : <FiChevronRight size={16} />}
                          </span>
                        )}
                      </button>

                      {!collapsed && isExpanded && (
                        <ul className="ml-6 mt-1 space-y-1">
                          {item.children.map((child) => {
                            const isChildActive = location.pathname === child.path ||
                                                (child.path !== '/' && location.pathname.startsWith(child.path));

                            return (
                              <li key={child.id}>
                                <Link
                                  to={child.path}
                                  className={`flex items-center py-2 px-3 rounded-lg ${
                                    isChildActive
                                      ? 'bg-blue-50 text-blue-600'
                                      : 'hover:bg-gray-100 text-gray-700'
                                  } transition-colors`}
                                >
                                  <span className={`${isChildActive ? 'text-blue-600' : 'text-gray-500'} mr-3`}>
                                    {renderIcon(child.icon, 16)}
                                  </span>
                                  <span className={`${isChildActive ? 'font-medium' : ''}`}>{child.label}</span>
                                </Link>
                              </li>
                            );
                          })}
                        </ul>
                      )}
                    </div>
                  ) : (
                    <Link
                      to={item.path}
                      className={`flex items-center py-2 px-3 rounded-lg ${
                        isActive
                          ? 'bg-blue-50 text-blue-600'
                          : 'hover:bg-gray-100 text-gray-700'
                      } transition-colors`}
                    >
                      <span className={`${isActive ? 'text-blue-600' : 'text-gray-500'} ${collapsed ? '' : 'mr-3'}`}>
                        {renderIcon(item.icon)}
                      </span>
                      {!collapsed && <span className={`${isActive ? 'font-medium' : ''}`}>{item.label}</span>}
                    </Link>
                  )}
                </li>
              );
            })}
          </ul>
        </nav>
      </div>
    </div>
  );
};

export default Sidebar;
