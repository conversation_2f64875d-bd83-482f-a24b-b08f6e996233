import React from 'react';
import { 
  FiAward, 
  FiStar, 
  FiTarget, 
  FiTrendingUp, 
  FiUsers, 
  FiBook,
  FiClock,
  FiZap,
  FiHeart,
  FiShield
} from 'react-icons/fi';
import { formatNumber, formatDate } from '../../utils/helpers';

const AchievementCard = ({ 
  achievement, 
  userProgress = null, 
  onClick, 
  showProgress = true,
  size = 'normal' // 'small', 'normal', 'large'
}) => {
  const {
    id,
    name,
    description,
    type,
    category,
    icon,
    rarity,
    points,
    requirements,
    isActive = true,
    createdAt
  } = achievement;

  const isUnlocked = userProgress?.isUnlocked || false;
  const progress = userProgress?.progress || 0;
  const maxProgress = requirements?.target || 100;
  const progressPercentage = Math.min((progress / maxProgress) * 100, 100);

  const getIcon = () => {
    const iconMap = {
      'award': FiAward,
      'star': FiStar,
      'target': FiTarget,
      'trending': FiTrendingUp,
      'users': FiUsers,
      'book': <PERSON>Book,
      'clock': Fi<PERSON>lock,
      'zap': FiZap,
      'heart': FiHeart,
      'shield': FiShield
    };
    
    const IconComponent = iconMap[icon] || FiAward;
    return <IconComponent size={size === 'large' ? 32 : size === 'small' ? 16 : 24} />;
  };

  const getRarityColor = () => {
    const colors = {
      'common': 'bg-gray-100 text-gray-800 border-gray-300',
      'uncommon': 'bg-green-100 text-green-800 border-green-300',
      'rare': 'bg-blue-100 text-blue-800 border-blue-300',
      'epic': 'bg-purple-100 text-purple-800 border-purple-300',
      'legendary': 'bg-yellow-100 text-yellow-800 border-yellow-300'
    };
    return colors[rarity] || colors.common;
  };

  const getRarityGlow = () => {
    if (!isUnlocked) return '';
    
    const glows = {
      'common': 'shadow-gray-200',
      'uncommon': 'shadow-green-200',
      'rare': 'shadow-blue-200',
      'epic': 'shadow-purple-200',
      'legendary': 'shadow-yellow-200'
    };
    return glows[rarity] || '';
  };

  const getCategoryIcon = () => {
    const categoryIcons = {
      'learning': FiBook,
      'social': FiUsers,
      'progress': FiTrendingUp,
      'time': FiClock,
      'special': FiStar
    };
    
    const CategoryIcon = categoryIcons[category] || FiTarget;
    return <CategoryIcon size={14} />;
  };

  const cardClasses = `
    relative bg-white rounded-lg border-2 transition-all duration-300 cursor-pointer
    ${isUnlocked 
      ? `${getRarityColor()} ${getRarityGlow()} shadow-lg hover:shadow-xl` 
      : 'border-gray-200 bg-gray-50 hover:bg-gray-100'
    }
    ${size === 'large' ? 'p-6' : size === 'small' ? 'p-3' : 'p-4'}
    ${onClick ? 'hover:scale-105' : ''}
  `;

  return (
    <div className={cardClasses} onClick={onClick}>
      {/* Rarity indicator */}
      {isUnlocked && (
        <div className="absolute top-2 right-2">
          <div className={`px-2 py-1 rounded-full text-xs font-medium ${getRarityColor()}`}>
            {rarity}
          </div>
        </div>
      )}

      {/* Achievement icon */}
      <div className="flex items-center justify-center mb-3">
        <div className={`
          p-3 rounded-full transition-all duration-300
          ${isUnlocked 
            ? `${getRarityColor().replace('text-', 'text-').replace('bg-', 'bg-')} shadow-md` 
            : 'bg-gray-200 text-gray-500'
          }
        `}>
          {getIcon()}
        </div>
      </div>

      {/* Achievement info */}
      <div className="text-center">
        <h3 className={`font-semibold mb-1 ${
          size === 'large' ? 'text-lg' : size === 'small' ? 'text-sm' : 'text-base'
        } ${isUnlocked ? 'text-gray-900' : 'text-gray-500'}`}>
          {name}
        </h3>
        
        <p className={`text-gray-600 mb-3 ${
          size === 'large' ? 'text-sm' : 'text-xs'
        }`}>
          {description}
        </p>

        {/* Category and points */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-1 text-xs text-gray-500">
            {getCategoryIcon()}
            <span className="capitalize">{category}</span>
          </div>
          <div className="flex items-center gap-1 text-xs font-medium text-yellow-600">
            <FiStar size={12} />
            <span>{formatNumber(points)} pts</span>
          </div>
        </div>

        {/* Progress bar */}
        {showProgress && requirements && (
          <div className="mb-3">
            <div className="flex justify-between text-xs text-gray-600 mb-1">
              <span>Прогресс</span>
              <span>{formatNumber(progress)} / {formatNumber(maxProgress)}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-500 ${
                  isUnlocked 
                    ? 'bg-gradient-to-r from-green-400 to-green-600' 
                    : 'bg-gradient-to-r from-blue-400 to-blue-600'
                }`}
                style={{ width: `${progressPercentage}%` }}
              />
            </div>
            <div className="text-xs text-gray-500 mt-1">
              {progressPercentage.toFixed(1)}% завершено
            </div>
          </div>
        )}

        {/* Unlock status */}
        {isUnlocked ? (
          <div className="flex items-center justify-center gap-1 text-green-600 text-sm font-medium">
            <FiAward size={14} />
            <span>Получено</span>
            {userProgress?.unlockedAt && (
              <span className="text-xs text-gray-500 ml-1">
                {formatDate(userProgress.unlockedAt)}
              </span>
            )}
          </div>
        ) : (
          <div className="text-gray-500 text-sm">
            {progressPercentage > 0 ? 'В процессе' : 'Заблокировано'}
          </div>
        )}

        {/* Requirements */}
        {requirements && !isUnlocked && (
          <div className="mt-3 p-2 bg-gray-100 rounded text-xs text-gray-600">
            <strong>Требования:</strong> {requirements.description}
          </div>
        )}
      </div>

      {/* Special effects for legendary achievements */}
      {isUnlocked && rarity === 'legendary' && (
        <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 opacity-20 animate-pulse" />
      )}
    </div>
  );
};

export default AchievementCard;
