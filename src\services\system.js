import apiClient from './api';

export const systemService = {
  // Health check
  async getHealthCheck() {
    const response = await apiClient.get('/healthcheck');
    return response.data;
  },

  // System metrics
  async getSystemMetrics() {
    const response = await apiClient.get('/debug/vars');
    return response.data;
  },

  // Overview statistics (placeholder - needs backend implementation)
  async getOverviewStats() {
    // This endpoint needs to be implemented on the backend
    const response = await apiClient.get('/admin/stats/overview');
    return response.data;
  },

  // Module statistics (placeholder - needs backend implementation)
  async getModuleStats() {
    // This endpoint needs to be implemented on the backend
    const response = await apiClient.get('/admin/stats/modules');
    return response.data;
  },

  // System settings (placeholder - needs backend implementation)
  async getSystemSettings() {
    // This endpoint needs to be implemented on the backend
    const response = await apiClient.get('/admin/settings');
    return response.data;
  },

  // Update system settings (placeholder - needs backend implementation)
  async updateSystemSettings(settings) {
    // This endpoint needs to be implemented on the backend
    const response = await apiClient.put('/admin/settings', settings);
    return response.data;
  }
};

export default systemService;
