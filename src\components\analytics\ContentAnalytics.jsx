import React from 'react';
import { Bar, Line, Pie } from 'react-chartjs-2';
import {
  FiFileText,
  FiHelpCircle,
  FiType,
  FiPackage,
  FiTrendingUp,
  FiEye,
  FiThumbsUp
} from 'react-icons/fi';
import { formatNumber } from '../../utils/helpers';

const StatCard = ({ title, value, change, icon: Icon, color = 'blue' }) => {
  const isPositive = change > 0;
  
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{formatNumber(value)}</p>
          {change !== undefined && (
            <p className={`text-sm ${isPositive ? 'text-green-600' : 'text-red-600'} flex items-center gap-1`}>
              <FiTrendingUp size={14} className={isPositive ? '' : 'rotate-180'} />
              {isPositive ? '+' : ''}{change}% за месяц
            </p>
          )}
        </div>
        <div className={`p-3 rounded-full bg-${color}-100`}>
          <Icon className={`text-${color}-600`} size={24} />
        </div>
      </div>
    </div>
  );
};

const ContentAnalytics = ({ data }) => {
  const {
    totalWords = 0,
    totalSentences = 0,
    totalQuestions = 0,
    totalTheories = 0,
    totalModules = 0,
    contentGrowth = {},
    contentUsage = [],
    popularContent = [],
    contentByType = [],
    contentCreation = []
  } = data;

  // Content creation over time
  const creationChartData = {
    labels: contentCreation.map(item => item.date),
    datasets: [
      {
        label: 'Слова',
        data: contentCreation.map(item => item.words || 0),
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
      },
      {
        label: 'Предложения',
        data: contentCreation.map(item => item.sentences || 0),
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        tension: 0.4,
      },
      {
        label: 'Вопросы',
        data: contentCreation.map(item => item.questions || 0),
        borderColor: 'rgb(251, 191, 36)',
        backgroundColor: 'rgba(251, 191, 36, 0.1)',
        tension: 0.4,
      },
      {
        label: 'Теории',
        data: contentCreation.map(item => item.theories || 0),
        borderColor: 'rgb(239, 68, 68)',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        tension: 0.4,
      },
    ],
  };

  // Content usage chart
  const usageChartData = {
    labels: contentUsage.map(item => item.name),
    datasets: [
      {
        label: 'Просмотры',
        data: contentUsage.map(item => item.views),
        backgroundColor: 'rgba(59, 130, 246, 0.8)',
      },
      {
        label: 'Взаимодействия',
        data: contentUsage.map(item => item.interactions),
        backgroundColor: 'rgba(34, 197, 94, 0.8)',
      },
    ],
  };

  // Content distribution by type
  const typeChartData = {
    labels: contentByType.map(item => item.type),
    datasets: [
      {
        data: contentByType.map(item => item.count),
        backgroundColor: [
          'rgba(59, 130, 246, 0.8)',
          'rgba(34, 197, 94, 0.8)',
          'rgba(251, 191, 36, 0.8)',
          'rgba(239, 68, 68, 0.8)',
          'rgba(168, 85, 247, 0.8)',
        ],
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  const pieOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'bottom',
      },
    },
  };

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <StatCard
          title="Слова"
          value={totalWords}
          change={contentGrowth.words}
          icon={FiType}
          color="blue"
        />
        <StatCard
          title="Предложения"
          value={totalSentences}
          change={contentGrowth.sentences}
          icon={FiFileText}
          color="green"
        />
        <StatCard
          title="Вопросы"
          value={totalQuestions}
          change={contentGrowth.questions}
          icon={FiHelpCircle}
          color="yellow"
        />
        <StatCard
          title="Теории"
          value={totalTheories}
          change={contentGrowth.theories}
          icon={FiFileText}
          color="red"
        />
        <StatCard
          title="Модули"
          value={totalModules}
          change={contentGrowth.modules}
          icon={FiPackage}
          color="purple"
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Content Creation Timeline */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Создание контента по времени
          </h3>
          <div className="h-64">
            <Line data={creationChartData} options={chartOptions} />
          </div>
        </div>

        {/* Content Usage */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Использование контента
          </h3>
          <div className="h-64">
            <Bar data={usageChartData} options={chartOptions} />
          </div>
        </div>
      </div>

      {/* Content Distribution and Popular Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Content Distribution */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Распределение контента по типам
          </h3>
          <div className="h-64 flex items-center justify-center">
            <div className="w-48 h-48">
              <Pie data={typeChartData} options={pieOptions} />
            </div>
          </div>
        </div>

        {/* Popular Content */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Популярный контент
          </h3>
          <div className="space-y-3">
            {popularContent.slice(0, 8).map((item, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                <div className="flex items-center gap-3">
                  <div className="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-full text-sm font-medium">
                    {index + 1}
                  </div>
                  <div>
                    <div className="font-medium text-sm text-gray-900">{item.title}</div>
                    <div className="text-xs text-gray-500">{item.type}</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="flex items-center gap-1 text-sm text-gray-600">
                    <FiEye size={14} />
                    <span>{formatNumber(item.views)}</span>
                  </div>
                  <div className="flex items-center gap-1 text-sm text-gray-600">
                    <FiThumbsUp size={14} />
                    <span>{formatNumber(item.likes)}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Content Quality Metrics */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Метрики качества контента
          </h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {formatNumber(totalWords + totalSentences + totalQuestions + totalTheories)}
              </div>
              <div className="text-sm text-gray-600">Всего элементов</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {formatNumber(contentUsage.reduce((sum, item) => sum + item.views, 0))}
              </div>
              <div className="text-sm text-gray-600">Общие просмотры</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {formatNumber(contentUsage.reduce((sum, item) => sum + item.interactions, 0))}
              </div>
              <div className="text-sm text-gray-600">Взаимодействия</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {totalModules > 0 ? Math.round((totalWords + totalSentences + totalQuestions + totalTheories) / totalModules) : 0}
              </div>
              <div className="text-sm text-gray-600">Элементов на модуль</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContentAnalytics;
