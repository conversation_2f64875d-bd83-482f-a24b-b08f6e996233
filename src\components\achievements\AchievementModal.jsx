import React, { useState, useEffect } from 'react';
import { FiX, FiAward, FiStar, FiTarget, FiBook, FiUsers, FiClock, FiZap, FiHeart, FiShield } from 'react-icons/fi';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { ACHIEVEMENT_TYPES, ACHIEVEMENT_CATEGORIES, ACHIEVEMENT_RARITIES } from '../../utils/constants';

// Validation schema
const schema = yup.object({
  name: yup
    .string()
    .required('Название достижения обязательно')
    .min(3, 'Минимум 3 символа'),
  description: yup
    .string()
    .required('Описание обязательно')
    .min(10, 'Минимум 10 символов'),
  type: yup
    .string()
    .required('Тип достижения обязателен'),
  category: yup
    .string()
    .required('Категория обязательна'),
  rarity: yup
    .string()
    .required('Редкость обязательна'),
  points: yup
    .number()
    .required('Количество очков обязательно')
    .min(1, 'Минимум 1 очко')
    .max(10000, 'Максимум 10000 очков'),
  icon: yup
    .string()
    .required('Иконка обязательна'),
  requirements: yup.object({
    type: yup.string().required(),
    target: yup.number().required('Цель обязательна').min(1),
    description: yup.string().required('Описание требований обязательно')
  })
});

const AchievementModal = ({ achievement, isOpen, onClose, onSave, isLoading }) => {
  const [previewIcon, setPreviewIcon] = useState('award');

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      type: ACHIEVEMENT_TYPES.PROGRESS,
      category: ACHIEVEMENT_CATEGORIES.LEARNING,
      rarity: ACHIEVEMENT_RARITIES.COMMON,
      icon: 'award',
      points: 100,
      isActive: true,
      requirements: {
        type: 'count',
        target: 1,
        description: ''
      }
    }
  });

  const watchedIcon = watch('icon');
  const watchedType = watch('type');
  const watchedRarity = watch('rarity');

  useEffect(() => {
    if (watchedIcon) {
      setPreviewIcon(watchedIcon);
    }
  }, [watchedIcon]);

  useEffect(() => {
    if (isOpen && achievement) {
      // Edit mode - populate form
      Object.keys(achievement).forEach(key => {
        if (key === 'requirements') {
          setValue('requirements.type', achievement.requirements?.type || 'count');
          setValue('requirements.target', achievement.requirements?.target || 1);
          setValue('requirements.description', achievement.requirements?.description || '');
        } else {
          setValue(key, achievement[key]);
        }
      });
      setPreviewIcon(achievement.icon || 'award');
    } else if (isOpen) {
      // Create mode - reset form
      reset();
      setPreviewIcon('award');
    }
  }, [isOpen, achievement, setValue, reset]);

  const iconOptions = [
    { value: 'award', label: 'Награда', icon: FiAward },
    { value: 'star', label: 'Звезда', icon: FiStar },
    { value: 'target', label: 'Цель', icon: FiTarget },
    { value: 'book', label: 'Книга', icon: FiBook },
    { value: 'users', label: 'Пользователи', icon: FiUsers },
    { value: 'clock', label: 'Время', icon: FiClock },
    { value: 'zap', label: 'Молния', icon: FiZap },
    { value: 'heart', label: 'Сердце', icon: FiHeart },
    { value: 'shield', label: 'Щит', icon: FiShield }
  ];

  const typeOptions = [
    { value: ACHIEVEMENT_TYPES.PROGRESS, label: 'Прогресс' },
    { value: ACHIEVEMENT_TYPES.MILESTONE, label: 'Веха' },
    { value: ACHIEVEMENT_TYPES.STREAK, label: 'Серия' },
    { value: ACHIEVEMENT_TYPES.COLLECTION, label: 'Коллекция' },
    { value: ACHIEVEMENT_TYPES.SPECIAL, label: 'Особое' }
  ];

  const categoryOptions = [
    { value: ACHIEVEMENT_CATEGORIES.LEARNING, label: 'Обучение' },
    { value: ACHIEVEMENT_CATEGORIES.SOCIAL, label: 'Социальное' },
    { value: ACHIEVEMENT_CATEGORIES.PROGRESS, label: 'Прогресс' },
    { value: ACHIEVEMENT_CATEGORIES.TIME, label: 'Время' },
    { value: ACHIEVEMENT_CATEGORIES.SPECIAL, label: 'Особое' }
  ];

  const rarityOptions = [
    { value: ACHIEVEMENT_RARITIES.COMMON, label: 'Обычное', points: 50 },
    { value: ACHIEVEMENT_RARITIES.UNCOMMON, label: 'Необычное', points: 100 },
    { value: ACHIEVEMENT_RARITIES.RARE, label: 'Редкое', points: 250 },
    { value: ACHIEVEMENT_RARITIES.EPIC, label: 'Эпическое', points: 500 },
    { value: ACHIEVEMENT_RARITIES.LEGENDARY, label: 'Легендарное', points: 1000 }
  ];

  const requirementTypes = [
    { value: 'count', label: 'Количество' },
    { value: 'streak', label: 'Серия' },
    { value: 'time', label: 'Время' },
    { value: 'percentage', label: 'Процент' }
  ];

  const getPreviewIcon = () => {
    const iconOption = iconOptions.find(opt => opt.value === previewIcon);
    if (iconOption) {
      const IconComponent = iconOption.icon;
      return <IconComponent size={32} />;
    }
    return <FiAward size={32} />;
  };

  const getRarityColor = (rarity) => {
    const colors = {
      [ACHIEVEMENT_RARITIES.COMMON]: 'bg-gray-100 text-gray-800',
      [ACHIEVEMENT_RARITIES.UNCOMMON]: 'bg-green-100 text-green-800',
      [ACHIEVEMENT_RARITIES.RARE]: 'bg-blue-100 text-blue-800',
      [ACHIEVEMENT_RARITIES.EPIC]: 'bg-purple-100 text-purple-800',
      [ACHIEVEMENT_RARITIES.LEGENDARY]: 'bg-yellow-100 text-yellow-800'
    };
    return colors[rarity] || colors[ACHIEVEMENT_RARITIES.COMMON];
  };

  const onSubmit = (data) => {
    onSave(data);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {achievement ? 'Редактировать достижение' : 'Создать достижение'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <FiX size={24} />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="p-6">
          <div className="space-y-6">
            {/* Preview */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-3">Предварительный просмотр</h4>
              <div className="flex items-center justify-center">
                <div className={`p-4 rounded-full ${getRarityColor(watchedRarity)}`}>
                  {getPreviewIcon()}
                </div>
              </div>
            </div>

            {/* Basic Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Название *
                </label>
                <input
                  {...register('name')}
                  type="text"
                  className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.name ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="Название достижения"
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                )}
              </div>

              {/* Points */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Очки *
                </label>
                <input
                  {...register('points', { valueAsNumber: true })}
                  type="number"
                  min="1"
                  max="10000"
                  className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.points ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="100"
                />
                {errors.points && (
                  <p className="mt-1 text-sm text-red-600">{errors.points.message}</p>
                )}
              </div>
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Описание *
              </label>
              <textarea
                {...register('description')}
                rows={3}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.description ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Описание достижения"
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
              )}
            </div>

            {/* Type, Category, Rarity */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Тип *
                </label>
                <select
                  {...register('type')}
                  className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.type ? 'border-red-300' : 'border-gray-300'
                  }`}
                >
                  {typeOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Категория *
                </label>
                <select
                  {...register('category')}
                  className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.category ? 'border-red-300' : 'border-gray-300'
                  }`}
                >
                  {categoryOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Редкость *
                </label>
                <select
                  {...register('rarity')}
                  className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.rarity ? 'border-red-300' : 'border-gray-300'
                  }`}
                >
                  {rarityOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label} ({option.points} pts)
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Icon */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Иконка *
              </label>
              <div className="grid grid-cols-3 md:grid-cols-5 gap-2">
                {iconOptions.map(option => {
                  const IconComponent = option.icon;
                  return (
                    <label key={option.value} className="cursor-pointer">
                      <input
                        {...register('icon')}
                        type="radio"
                        value={option.value}
                        className="sr-only"
                      />
                      <div className={`p-3 border-2 rounded-lg text-center transition-colors ${
                        previewIcon === option.value
                          ? 'border-blue-500 bg-blue-50 text-blue-600'
                          : 'border-gray-300 hover:border-gray-400'
                      }`}>
                        <IconComponent size={20} className="mx-auto mb-1" />
                        <span className="text-xs">{option.label}</span>
                      </div>
                    </label>
                  );
                })}
              </div>
            </div>

            {/* Requirements */}
            <div className="border-t pt-6">
              <h4 className="font-medium text-gray-900 mb-4">Требования</h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Тип требования *
                  </label>
                  <select
                    {...register('requirements.type')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    {requirementTypes.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Цель *
                  </label>
                  <input
                    {...register('requirements.target', { valueAsNumber: true })}
                    type="number"
                    min="1"
                    className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                      errors.requirements?.target ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="1"
                  />
                  {errors.requirements?.target && (
                    <p className="mt-1 text-sm text-red-600">{errors.requirements.target.message}</p>
                  )}
                </div>
              </div>

              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Описание требований *
                </label>
                <input
                  {...register('requirements.description')}
                  type="text"
                  className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.requirements?.description ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="Например: Изучите 10 новых слов"
                />
                {errors.requirements?.description && (
                  <p className="mt-1 text-sm text-red-600">{errors.requirements.description.message}</p>
                )}
              </div>
            </div>

            {/* Active status */}
            <div className="flex items-center">
              <input
                {...register('isActive')}
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-900">
                Активное достижение
              </label>
            </div>
          </div>

          {/* Footer */}
          <div className="flex justify-end gap-3 mt-6 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Отмена
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Сохранение...' : 'Сохранить'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AchievementModal;
