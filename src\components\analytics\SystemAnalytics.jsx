import React from 'react';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import {
  FiServer,
  FiDatabase,
  FiHardDrive,
  FiCpu,
  FiActivity,
  FiAlertTriangle,
  FiCheckCircle,
  FiClock
} from 'react-icons/fi';
import { formatFileSize, formatNumber } from '../../utils/helpers';

const MetricCard = ({ title, value, unit, status, icon: Icon, color = 'blue' }) => {
  const getStatusColor = () => {
    switch (status) {
      case 'good': return 'text-green-600';
      case 'warning': return 'text-yellow-600';
      case 'critical': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'good': return <FiCheckCircle className="text-green-600" size={16} />;
      case 'warning': return <FiAlertTriangle className="text-yellow-600" size={16} />;
      case 'critical': return <FiAlertTriangle className="text-red-600" size={16} />;
      default: return <FiActivity className="text-gray-600" size={16} />;
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <div className="flex items-center justify-between mb-4">
        <div className={`p-3 rounded-full bg-${color}-100`}>
          <Icon className={`text-${color}-600`} size={24} />
        </div>
        {getStatusIcon()}
      </div>
      <div>
        <p className="text-sm font-medium text-gray-600">{title}</p>
        <p className={`text-2xl font-bold ${getStatusColor()}`}>
          {typeof value === 'number' && unit === 'bytes' ? formatFileSize(value) : formatNumber(value)}
          {unit && unit !== 'bytes' && <span className="text-sm ml-1">{unit}</span>}
        </p>
      </div>
    </div>
  );
};

const SystemAnalytics = ({ data }) => {
  const {
    serverMetrics = {},
    databaseMetrics = {},
    storageMetrics = {},
    performanceMetrics = [],
    errorLogs = [],
    systemLoad = [],
    responseTime = [],
    uptime = 0
  } = data;

  // Performance metrics chart
  const performanceChartData = {
    labels: performanceMetrics.map(item => item.time),
    datasets: [
      {
        label: 'CPU (%)',
        data: performanceMetrics.map(item => item.cpu),
        borderColor: 'rgb(239, 68, 68)',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        tension: 0.4,
        yAxisID: 'y',
      },
      {
        label: 'Memory (%)',
        data: performanceMetrics.map(item => item.memory),
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        yAxisID: 'y',
      },
      {
        label: 'Disk I/O (MB/s)',
        data: performanceMetrics.map(item => item.diskIO),
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        tension: 0.4,
        yAxisID: 'y1',
      },
    ],
  };

  // Response time chart
  const responseTimeChartData = {
    labels: responseTime.map(item => item.time),
    datasets: [
      {
        label: 'Время ответа (мс)',
        data: responseTime.map(item => item.value),
        backgroundColor: 'rgba(168, 85, 247, 0.8)',
      },
    ],
  };

  // Error distribution
  const errorChartData = {
    labels: ['4xx Errors', '5xx Errors', 'Database Errors', 'Network Errors'],
    datasets: [
      {
        data: [
          errorLogs.filter(e => e.type === '4xx').length,
          errorLogs.filter(e => e.type === '5xx').length,
          errorLogs.filter(e => e.type === 'database').length,
          errorLogs.filter(e => e.type === 'network').length,
        ],
        backgroundColor: [
          'rgba(251, 191, 36, 0.8)',
          'rgba(239, 68, 68, 0.8)',
          'rgba(168, 85, 247, 0.8)',
          'rgba(59, 130, 246, 0.8)',
        ],
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
    },
    scales: {
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        beginAtZero: true,
        max: 100,
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        beginAtZero: true,
        grid: {
          drawOnChartArea: false,
        },
      },
    },
  };

  const simpleChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  const doughnutOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'bottom',
      },
    },
  };

  const formatUptime = (seconds) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${days}д ${hours}ч ${minutes}м`;
  };

  return (
    <div className="space-y-6">
      {/* System Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="CPU Usage"
          value={serverMetrics.cpuUsage || 0}
          unit="%"
          status={serverMetrics.cpuUsage > 80 ? 'critical' : serverMetrics.cpuUsage > 60 ? 'warning' : 'good'}
          icon={FiCpu}
          color="red"
        />
        <MetricCard
          title="Memory Usage"
          value={serverMetrics.memoryUsage || 0}
          unit="%"
          status={serverMetrics.memoryUsage > 85 ? 'critical' : serverMetrics.memoryUsage > 70 ? 'warning' : 'good'}
          icon={FiServer}
          color="blue"
        />
        <MetricCard
          title="Disk Usage"
          value={storageMetrics.used || 0}
          unit="bytes"
          status={storageMetrics.usagePercent > 90 ? 'critical' : storageMetrics.usagePercent > 75 ? 'warning' : 'good'}
          icon={FiHardDrive}
          color="green"
        />
        <MetricCard
          title="Database Size"
          value={databaseMetrics.size || 0}
          unit="bytes"
          status="good"
          icon={FiDatabase}
          color="purple"
        />
      </div>

      {/* Performance Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* System Performance */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Производительность системы
          </h3>
          <div className="h-64">
            <Line data={performanceChartData} options={chartOptions} />
          </div>
        </div>

        {/* Response Time */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Время ответа API
          </h3>
          <div className="h-64">
            <Bar data={responseTimeChartData} options={simpleChartOptions} />
          </div>
        </div>
      </div>

      {/* Error Analysis and Uptime */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Error Distribution */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Распределение ошибок
          </h3>
          <div className="h-64 flex items-center justify-center">
            <div className="w-48 h-48">
              <Doughnut data={errorChartData} options={doughnutOptions} />
            </div>
          </div>
        </div>

        {/* System Info */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Информация о системе
          </h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
              <div className="flex items-center gap-2">
                <FiClock className="text-green-600" size={16} />
                <span className="text-sm font-medium">Время работы</span>
              </div>
              <span className="text-sm text-gray-600">{formatUptime(uptime)}</span>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
              <div className="flex items-center gap-2">
                <FiServer className="text-blue-600" size={16} />
                <span className="text-sm font-medium">Активные соединения</span>
              </div>
              <span className="text-sm text-gray-600">{serverMetrics.activeConnections || 0}</span>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
              <div className="flex items-center gap-2">
                <FiDatabase className="text-purple-600" size={16} />
                <span className="text-sm font-medium">Запросы к БД/сек</span>
              </div>
              <span className="text-sm text-gray-600">{databaseMetrics.queriesPerSecond || 0}</span>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
              <div className="flex items-center gap-2">
                <FiHardDrive className="text-green-600" size={16} />
                <span className="text-sm font-medium">Свободное место</span>
              </div>
              <span className="text-sm text-gray-600">
                {formatFileSize(storageMetrics.free || 0)}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Errors */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Последние ошибки
          </h3>
        </div>
        <div className="p-6">
          {errorLogs.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <FiCheckCircle size={48} className="mx-auto mb-4 text-green-500" />
              <p>Ошибок не обнаружено</p>
            </div>
          ) : (
            <div className="space-y-3">
              {errorLogs.slice(0, 10).map((error, index) => (
                <div key={index} className="flex items-start gap-3 p-3 bg-red-50 border border-red-200 rounded-md">
                  <FiAlertTriangle className="text-red-600 mt-0.5" size={16} />
                  <div className="flex-1">
                    <div className="font-medium text-sm text-red-900">{error.message}</div>
                    <div className="text-xs text-red-600 mt-1">
                      {error.timestamp} • {error.type} • {error.source}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SystemAnalytics;
