import React, { useState } from 'react';
import { FiX, FiDownload, FiExternalLink, FiCopy, FiTrash2 } from 'react-icons/fi';
import { formatFileSize, formatDate, isAudioFile, isImageFile } from '../utils/helpers';
import toast from 'react-hot-toast';

const FilePreview = ({ file, isOpen, onClose, onDownload, onDelete, onCopyUrl }) => {
  const [imageError, setImageError] = useState(false);

  if (!isOpen || !file) return null;

  const handleCopyUrl = () => {
    navigator.clipboard.writeText(file.url);
    toast.success('URL скопирован в буфер обмена');
    if (onCopyUrl) onCopyUrl(file);
  };

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = file.url;
    link.download = file.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    if (onDownload) onDownload(file);
  };

  const handleDelete = () => {
    if (window.confirm(`Вы уверены, что хотите удалить файл "${file.name}"?`)) {
      if (onDelete) onDelete(file);
      onClose();
    }
  };

  const renderPreview = () => {
    if (isImageFile(file.name) && !imageError) {
      return (
        <div className="flex items-center justify-center bg-gray-100 rounded-lg p-4">
          <img
            src={file.url}
            alt={file.name}
            className="max-w-full max-h-96 object-contain rounded"
            onError={() => setImageError(true)}
          />
        </div>
      );
    }

    if (isAudioFile(file.name)) {
      return (
        <div className="flex items-center justify-center bg-gray-100 rounded-lg p-8">
          <div className="text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM15.657 6.343a1 1 0 011.414 0A9.972 9.972 0 0119 12a9.972 9.972 0 01-1.929 5.657 1 1 0 11-1.414-1.414A7.971 7.971 0 0017 12a7.971 7.971 0 00-1.343-4.243 1 1 0 010-1.414z" clipRule="evenodd" />
                <path fillRule="evenodd" d="M13.828 8.172a1 1 0 011.414 0A5.983 5.983 0 0117 12a5.983 5.983 0 01-1.758 3.828 1 1 0 11-1.414-1.414A3.987 3.987 0 0015 12a3.987 3.987 0 00-1.172-2.828 1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </div>
            <audio controls className="w-full max-w-md">
              <source src={file.url} type={file.type || 'audio/mpeg'} />
              Ваш браузер не поддерживает аудио элемент.
            </audio>
          </div>
        </div>
      );
    }

    // Default preview for other file types
    return (
      <div className="flex items-center justify-center bg-gray-100 rounded-lg p-8">
        <div className="text-center">
          <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
            </svg>
          </div>
          <p className="text-gray-600">Предварительный просмотр недоступен</p>
          <p className="text-sm text-gray-500 mt-1">Нажмите "Скачать" для просмотра файла</p>
        </div>
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex-1 min-w-0">
            <h2 className="text-lg font-semibold text-gray-900 truncate">
              {file.name}
            </h2>
            <div className="flex items-center gap-4 mt-1 text-sm text-gray-500">
              <span>{formatFileSize(file.size)}</span>
              <span>{formatDate(file.lastModified)}</span>
              {file.type && <span>{file.type}</span>}
            </div>
          </div>
          
          <div className="flex items-center gap-2 ml-4">
            <button
              onClick={handleCopyUrl}
              className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors"
              title="Копировать URL"
            >
              <FiCopy size={18} />
            </button>
            
            <button
              onClick={() => window.open(file.url, '_blank')}
              className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors"
              title="Открыть в новой вкладке"
            >
              <FiExternalLink size={18} />
            </button>
            
            <button
              onClick={handleDownload}
              className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded-md transition-colors"
              title="Скачать"
            >
              <FiDownload size={18} />
            </button>
            
            <button
              onClick={handleDelete}
              className="p-2 text-red-600 hover:text-red-800 hover:bg-red-100 rounded-md transition-colors"
              title="Удалить"
            >
              <FiTrash2 size={18} />
            </button>
            
            <button
              onClick={onClose}
              className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors"
              title="Закрыть"
            >
              <FiX size={18} />
            </button>
          </div>
        </div>

        {/* Preview content */}
        <div className="p-4 max-h-[calc(90vh-120px)] overflow-auto">
          {renderPreview()}
        </div>

        {/* Footer with file info */}
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between text-sm">
            <div className="text-gray-600">
              <strong>URL:</strong> 
              <span className="ml-2 font-mono text-xs bg-gray-200 px-2 py-1 rounded">
                {file.url}
              </span>
            </div>
            <div className="flex items-center gap-4">
              <button
                onClick={handleCopyUrl}
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                Копировать URL
              </button>
              <button
                onClick={handleDownload}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Скачать файл
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FilePreview;
